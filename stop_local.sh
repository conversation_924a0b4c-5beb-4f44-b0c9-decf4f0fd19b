#!/bin/bash

# Copy Trading System - Stop Local Development

echo "🛑 Stopping Copy Trading System..."

# Kill backend process
if [ -f "backend.pid" ]; then
    BACKEND_PID=$(cat backend.pid)
    if ps -p $BACKEND_PID > /dev/null; then
        kill $BACKEND_PID
        echo "✅ Backend stopped (PID: $BACKEND_PID)"
    else
        echo "⚠️  Backend process not found"
    fi
    rm backend.pid
fi

# Kill frontend process
if [ -f "frontend.pid" ]; then
    FRONTEND_PID=$(cat frontend.pid)
    if ps -p $FRONTEND_PID > /dev/null; then
        kill $FRONTEND_PID
        echo "✅ Frontend stopped (PID: $FRONTEND_PID)"
    else
        echo "⚠️  Frontend process not found"
    fi
    rm frontend.pid
fi

# Kill any remaining Node.js processes (React dev server)
pkill -f "react-scripts start" 2>/dev/null && echo "✅ React dev server stopped"

# Kill any remaining Python processes
pkill -f "main.py" 2>/dev/null && echo "✅ Python backend stopped"

echo "🎉 Copy Trading System stopped successfully!"

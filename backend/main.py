"""
Copy Trading System Backend API
FastAPI-based REST API and WebSocket server
"""

from fastapi import FastAPI, HTTPException, Depends, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import uvicorn
import asyncio
import logging
from datetime import datetime
from typing import List, Dict, Optional
import json

from api.routes import signals, providers, followers, auth, health
from api.models.database import init_db
from api.services.websocket_manager import WebSocketManager
from api.services.signal_processor import SignalProcessor
from api.services.redis_client import RedisClient
from api.middleware.auth import verify_token
from api.config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Copy Trading System API",
    description="Professional Copy Trading Platform API",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize services
websocket_manager = WebSocketManager()
signal_processor = SignalProcessor()
redis_client = RedisClient()

# Security
security = HTTPBearer()

@app.on_event("startup")
async def startup_event():
    """Initialize services on startup"""
    logger.info("Starting Copy Trading System API...")
    
    # Initialize database
    await init_db()
    
    # Initialize Redis
    await redis_client.connect()
    
    # Initialize signal processor
    await signal_processor.start()
    
    logger.info("API startup completed successfully")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    logger.info("Shutting down Copy Trading System API...")
    
    # Cleanup services
    await signal_processor.stop()
    await redis_client.disconnect()
    
    logger.info("API shutdown completed")

# Include routers
app.include_router(health.router, prefix="/api/v1", tags=["health"])
app.include_router(auth.router, prefix="/api/v1/auth", tags=["authentication"])
app.include_router(providers.router, prefix="/api/v1/providers", tags=["providers"])
app.include_router(followers.router, prefix="/api/v1/followers", tags=["followers"])
app.include_router(signals.router, prefix="/api/v1/signals", tags=["signals"])

@app.websocket("/ws/signals")
async def websocket_endpoint(websocket: WebSocket, token: str = None):
    """WebSocket endpoint for real-time signal distribution"""
    try:
        # Verify authentication
        if not token:
            await websocket.close(code=4001, reason="Authentication required")
            return
        
        user_data = await verify_token(token)
        if not user_data:
            await websocket.close(code=4001, reason="Invalid token")
            return
        
        # Accept connection
        await websocket.accept()
        
        # Register client
        client_id = f"{user_data['user_type']}_{user_data['user_id']}"
        await websocket_manager.connect(client_id, websocket, user_data)
        
        logger.info(f"WebSocket client connected: {client_id}")
        
        try:
            # Keep connection alive and handle incoming messages
            while True:
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # Handle different message types
                if message.get("type") == "ping":
                    await websocket.send_text(json.dumps({
                        "type": "pong",
                        "timestamp": datetime.utcnow().isoformat()
                    }))
                elif message.get("type") == "subscribe":
                    # Handle subscription to specific providers
                    await handle_subscription(client_id, message)
                
        except WebSocketDisconnect:
            logger.info(f"WebSocket client disconnected: {client_id}")
        except Exception as e:
            logger.error(f"WebSocket error for {client_id}: {e}")
        finally:
            await websocket_manager.disconnect(client_id)
            
    except Exception as e:
        logger.error(f"WebSocket connection error: {e}")
        await websocket.close(code=4000, reason="Connection error")

async def handle_subscription(client_id: str, message: Dict):
    """Handle subscription requests"""
    provider_ids = message.get("provider_ids", [])
    
    # Update client subscriptions in Redis
    await redis_client.set_client_subscriptions(client_id, provider_ids)
    
    logger.info(f"Client {client_id} subscribed to providers: {provider_ids}")

@app.post("/api/v1/signals/broadcast")
async def broadcast_signal(
    signal_data: Dict,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """Broadcast signal to followers (internal endpoint)"""
    try:
        # Verify internal API token
        if credentials.credentials != settings.INTERNAL_API_TOKEN:
            raise HTTPException(status_code=401, detail="Invalid internal token")
        
        # Process and broadcast signal
        await signal_processor.process_signal(signal_data)
        
        return {"status": "success", "message": "Signal broadcasted"}
        
    except Exception as e:
        logger.error(f"Signal broadcast error: {e}")
        raise HTTPException(status_code=500, detail="Signal broadcast failed")

@app.get("/api/v1/stats")
async def get_system_stats(
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """Get system statistics"""
    try:
        # Verify admin token
        user_data = await verify_token(credentials.credentials)
        if not user_data or user_data.get("role") != "admin":
            raise HTTPException(status_code=403, detail="Admin access required")
        
        stats = {
            "connected_clients": await websocket_manager.get_client_count(),
            "active_providers": await redis_client.get_active_providers_count(),
            "active_followers": await redis_client.get_active_followers_count(),
            "signals_processed_today": await redis_client.get_daily_signal_count(),
            "system_uptime": datetime.utcnow().isoformat(),
        }
        
        return stats
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Stats error: {e}")
        raise HTTPException(status_code=500, detail="Failed to get stats")

# Error handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Handle HTTP exceptions"""
    return {
        "error": True,
        "message": exc.detail,
        "status_code": exc.status_code,
        "timestamp": datetime.utcnow().isoformat()
    }

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """Handle general exceptions"""
    logger.error(f"Unhandled exception: {exc}")
    return {
        "error": True,
        "message": "Internal server error",
        "status_code": 500,
        "timestamp": datetime.utcnow().isoformat()
    }

# Health check endpoints
@app.get("/health")
async def health_check():
    """Basic health check"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "2.0.0"
    }

@app.get("/health/detailed")
async def detailed_health_check():
    """Detailed health check"""
    try:
        # Check database connection
        db_healthy = await check_database_health()
        
        # Check Redis connection
        redis_healthy = await redis_client.ping()
        
        # Check WebSocket manager
        ws_healthy = websocket_manager.is_healthy()
        
        return {
            "status": "healthy" if all([db_healthy, redis_healthy, ws_healthy]) else "unhealthy",
            "components": {
                "database": "healthy" if db_healthy else "unhealthy",
                "redis": "healthy" if redis_healthy else "unhealthy",
                "websocket": "healthy" if ws_healthy else "unhealthy"
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Health check error: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }

async def check_database_health():
    """Check database connectivity"""
    try:
        from api.models.database import database
        await database.fetch_one("SELECT 1")
        return True
    except Exception:
        return False

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )

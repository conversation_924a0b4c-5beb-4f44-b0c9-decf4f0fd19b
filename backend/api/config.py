"""
Configuration settings for the Copy Trading System
"""

import os
from typing import List
from pydantic import BaseSettings, validator

class Settings(BaseSettings):
    # Application
    APP_NAME: str = "Copy Trading System"
    VERSION: str = "2.0.0"
    DEBUG: bool = False
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # Security
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # Database
    DATABASE_URL: str = "postgresql://copytrading:password@localhost:5432/copytrading_db"
    DATABASE_POOL_SIZE: int = 20
    DATABASE_MAX_OVERFLOW: int = 30
    
    # Redis
    REDIS_URL: str = "redis://localhost:6379/0"
    REDIS_PASSWORD: str = ""
    REDIS_DB: int = 0
    
    # CORS
    ALLOWED_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:8080",
        "https://yourdomain.com"
    ]
    
    # API Keys
    INTERNAL_API_TOKEN: str = "internal-api-token-change-in-production"
    
    # MT5 Settings
    MT5_POLL_INTERVAL: int = 1  # seconds
    MT5_MESSAGE_TTL: int = 300  # seconds (5 minutes)
    MAX_SIGNALS_PER_DAY: int = 100
    SIGNAL_EXPIRY_MINUTES: int = 60
    
    # Risk Management
    MAX_RISK_PER_TRADE: float = 5.0  # percentage
    MAX_DAILY_LOSS: float = 1000.0  # USD
    MAX_DRAWDOWN: float = 20.0  # percentage
    MIN_BALANCE_REQUIRED: float = 100.0  # USD
    
    # Rate Limiting
    RATE_LIMIT_PER_MINUTE: int = 60
    RATE_LIMIT_PER_HOUR: int = 1000
    
    # Email (for notifications)
    SMTP_HOST: str = "smtp.gmail.com"
    SMTP_PORT: int = 587
    SMTP_USER: str = ""
    SMTP_PASSWORD: str = ""
    SMTP_TLS: bool = True
    
    # Monitoring
    ENABLE_METRICS: bool = True
    METRICS_PORT: int = 9090
    LOG_LEVEL: str = "INFO"
    
    # File Storage
    UPLOAD_DIR: str = "uploads"
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    
    # WebSocket
    WS_HEARTBEAT_INTERVAL: int = 30  # seconds
    WS_MAX_CONNECTIONS: int = 1000
    
    # Performance
    WORKER_PROCESSES: int = 1
    WORKER_CONNECTIONS: int = 1000
    KEEPALIVE_TIMEOUT: int = 5
    
    @validator('ALLOWED_ORIGINS', pre=True)
    def assemble_cors_origins(cls, v):
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        return v
    
    @validator('DATABASE_URL', pre=True)
    def assemble_db_connection(cls, v):
        if isinstance(v, str):
            return v
        return f"postgresql://{v.get('user')}:{v.get('password')}@{v.get('host')}:{v.get('port')}/{v.get('database')}"
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# Create settings instance
settings = Settings()

# Environment-specific configurations
class DevelopmentSettings(Settings):
    DEBUG: bool = True
    LOG_LEVEL: str = "DEBUG"
    DATABASE_URL: str = "postgresql://copytrading:password@localhost:5432/copytrading_dev"
    REDIS_URL: str = "redis://localhost:6379/1"

class ProductionSettings(Settings):
    DEBUG: bool = False
    LOG_LEVEL: str = "INFO"
    WORKER_PROCESSES: int = 4
    
    # Override with environment variables in production
    SECRET_KEY: str = os.getenv("SECRET_KEY", "production-secret-key")
    DATABASE_URL: str = os.getenv("DATABASE_URL", "***********************************/copytrading")
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://prod-redis:6379/0")

class TestingSettings(Settings):
    DEBUG: bool = True
    DATABASE_URL: str = "postgresql://copytrading:password@localhost:5432/copytrading_test"
    REDIS_URL: str = "redis://localhost:6379/2"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 5  # Short expiry for testing

def get_settings() -> Settings:
    """Get settings based on environment"""
    env = os.getenv("ENVIRONMENT", "development").lower()
    
    if env == "production":
        return ProductionSettings()
    elif env == "testing":
        return TestingSettings()
    else:
        return DevelopmentSettings()

# Use environment-specific settings
settings = get_settings()

# Logging configuration
LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "default": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        },
        "detailed": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s - %(message)s",
        },
    },
    "handlers": {
        "default": {
            "formatter": "default",
            "class": "logging.StreamHandler",
            "stream": "ext://sys.stdout",
        },
        "file": {
            "formatter": "detailed",
            "class": "logging.handlers.RotatingFileHandler",
            "filename": "logs/copytrading.log",
            "maxBytes": 10485760,  # 10MB
            "backupCount": 5,
        },
    },
    "loggers": {
        "": {
            "level": settings.LOG_LEVEL,
            "handlers": ["default", "file"],
        },
        "uvicorn": {
            "level": "INFO",
            "handlers": ["default"],
            "propagate": False,
        },
        "databases": {
            "level": "WARNING",
            "handlers": ["default"],
            "propagate": False,
        },
    },
}

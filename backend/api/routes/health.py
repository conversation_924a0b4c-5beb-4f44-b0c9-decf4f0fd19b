"""
Health Check API Routes
"""

from fastapi import APIRouter, HTTPException, Depends
from fastapi.security import HTT<PERSON><PERSON>orizationCredentials, HTT<PERSON><PERSON>earer
from datetime import datetime
import logging
import asyncio
import psutil
import os

from ..models.database import database
from ..services.redis_client import RedisClient
from ..services.websocket_manager import WebSocketManager
from ..middleware.auth import verify_token

logger = logging.getLogger(__name__)
router = APIRouter()
security = HTTPBearer()

# Initialize services
redis_client = RedisClient()

@router.get("/health")
async def basic_health_check():
    """Basic health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "2.0.0",
        "service": "Copy Trading System API"
    }

@router.get("/health/detailed")
async def detailed_health_check():
    """Detailed health check with component status"""
    try:
        # Check all components
        components = {}
        overall_healthy = True
        
        # Database health
        try:
            await database.fetch_one("SELECT 1")
            components["database"] = {
                "status": "healthy",
                "response_time_ms": await get_database_response_time()
            }
        except Exception as e:
            components["database"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            overall_healthy = False
        
        # Redis health
        try:
            redis_healthy = await redis_client.ping()
            if redis_healthy:
                components["redis"] = {
                    "status": "healthy",
                    "memory_usage": await redis_client.get_memory_usage(),
                    "connected_clients": await redis_client.get_client_count()
                }
            else:
                components["redis"] = {"status": "unhealthy"}
                overall_healthy = False
        except Exception as e:
            components["redis"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            overall_healthy = False
        
        # WebSocket health
        try:
            from ..services.websocket_manager import websocket_manager
            ws_healthy = websocket_manager.is_healthy()
            components["websocket"] = {
                "status": "healthy" if ws_healthy else "unhealthy",
                "active_connections": await websocket_manager.get_client_count()
            }
            if not ws_healthy:
                overall_healthy = False
        except Exception as e:
            components["websocket"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            overall_healthy = False
        
        # System resources
        components["system"] = {
            "status": "healthy",
            "cpu_percent": psutil.cpu_percent(interval=1),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_percent": psutil.disk_usage('/').percent,
            "load_average": os.getloadavg() if hasattr(os, 'getloadavg') else None
        }
        
        # Check if system resources are concerning
        if (components["system"]["cpu_percent"] > 90 or 
            components["system"]["memory_percent"] > 90 or
            components["system"]["disk_percent"] > 90):
            components["system"]["status"] = "warning"
        
        return {
            "status": "healthy" if overall_healthy else "unhealthy",
            "timestamp": datetime.utcnow().isoformat(),
            "components": components,
            "version": "2.0.0"
        }
        
    except Exception as e:
        logger.error(f"Health check error: {e}")
        return {
            "status": "unhealthy",
            "timestamp": datetime.utcnow().isoformat(),
            "error": str(e),
            "version": "2.0.0"
        }

@router.get("/health/database")
async def database_health_check():
    """Database-specific health check"""
    try:
        start_time = datetime.utcnow()
        
        # Test basic connectivity
        await database.fetch_one("SELECT 1")
        
        # Test table access
        user_count = await database.fetch_val("SELECT COUNT(*) FROM users")
        signal_count = await database.fetch_val("SELECT COUNT(*) FROM signals WHERE created_at > NOW() - INTERVAL '24 hours'")
        
        response_time = (datetime.utcnow() - start_time).total_seconds() * 1000
        
        return {
            "status": "healthy",
            "response_time_ms": round(response_time, 2),
            "user_count": user_count,
            "signals_24h": signal_count,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Database health check error: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }

@router.get("/health/redis")
async def redis_health_check():
    """Redis-specific health check"""
    try:
        start_time = datetime.utcnow()
        
        # Test connectivity
        pong = await redis_client.ping()
        
        if not pong:
            return {
                "status": "unhealthy",
                "error": "Redis ping failed",
                "timestamp": datetime.utcnow().isoformat()
            }
        
        # Get Redis info
        memory_usage = await redis_client.get_memory_usage()
        client_count = await redis_client.get_client_count()
        
        response_time = (datetime.utcnow() - start_time).total_seconds() * 1000
        
        return {
            "status": "healthy",
            "response_time_ms": round(response_time, 2),
            "memory_usage_mb": memory_usage,
            "connected_clients": client_count,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Redis health check error: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }

@router.get("/health/system")
async def system_health_check():
    """System resource health check"""
    try:
        # CPU usage
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        
        # Memory usage
        memory = psutil.virtual_memory()
        
        # Disk usage
        disk = psutil.disk_usage('/')
        
        # Network stats
        network = psutil.net_io_counters()
        
        # Process info
        process = psutil.Process()
        process_memory = process.memory_info()
        
        # Load average (Unix only)
        load_avg = None
        if hasattr(os, 'getloadavg'):
            load_avg = os.getloadavg()
        
        # Determine status
        status = "healthy"
        if cpu_percent > 90 or memory.percent > 90 or disk.percent > 90:
            status = "warning"
        if cpu_percent > 95 or memory.percent > 95 or disk.percent > 95:
            status = "critical"
        
        return {
            "status": status,
            "cpu": {
                "percent": cpu_percent,
                "count": cpu_count,
                "load_average": load_avg
            },
            "memory": {
                "total_gb": round(memory.total / (1024**3), 2),
                "available_gb": round(memory.available / (1024**3), 2),
                "percent": memory.percent,
                "process_mb": round(process_memory.rss / (1024**2), 2)
            },
            "disk": {
                "total_gb": round(disk.total / (1024**3), 2),
                "free_gb": round(disk.free / (1024**3), 2),
                "percent": disk.percent
            },
            "network": {
                "bytes_sent": network.bytes_sent,
                "bytes_recv": network.bytes_recv,
                "packets_sent": network.packets_sent,
                "packets_recv": network.packets_recv
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"System health check error: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }

@router.get("/health/services")
async def services_health_check(
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """Services health check (admin only)"""
    try:
        # Verify admin access
        user_data = await verify_token(credentials.credentials)
        if not user_data or user_data.get("role") != "admin":
            raise HTTPException(status_code=403, detail="Admin access required")
        
        services = {}
        
        # Signal processor health
        try:
            from ..services.signal_processor import signal_processor
            services["signal_processor"] = {
                "status": "healthy" if signal_processor.is_running() else "stopped",
                "processed_signals": await signal_processor.get_processed_count(),
                "queue_size": await signal_processor.get_queue_size()
            }
        except Exception as e:
            services["signal_processor"] = {
                "status": "unhealthy",
                "error": str(e)
            }
        
        # WebSocket manager health
        try:
            from ..services.websocket_manager import websocket_manager
            services["websocket_manager"] = {
                "status": "healthy" if websocket_manager.is_healthy() else "unhealthy",
                "active_connections": await websocket_manager.get_client_count(),
                "total_messages": websocket_manager.get_message_count()
            }
        except Exception as e:
            services["websocket_manager"] = {
                "status": "unhealthy",
                "error": str(e)
            }
        
        # Active providers and followers
        try:
            active_providers = await redis_client.get_active_providers_count()
            active_followers = await redis_client.get_active_followers_count()
            
            services["mt5_clients"] = {
                "status": "healthy",
                "active_providers": active_providers,
                "active_followers": active_followers,
                "total_active": active_providers + active_followers
            }
        except Exception as e:
            services["mt5_clients"] = {
                "status": "unhealthy",
                "error": str(e)
            }
        
        return {
            "status": "healthy",
            "services": services,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Services health check error: {e}")
        raise HTTPException(status_code=500, detail="Services health check failed")

@router.get("/health/metrics")
async def health_metrics(
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """Health metrics for monitoring systems"""
    try:
        # Verify admin access
        user_data = await verify_token(credentials.credentials)
        if not user_data or user_data.get("role") != "admin":
            raise HTTPException(status_code=403, detail="Admin access required")
        
        # Collect metrics
        metrics = {}
        
        # Database metrics
        try:
            db_response_time = await get_database_response_time()
            metrics["database_response_time_ms"] = db_response_time
            metrics["database_status"] = 1  # 1 = healthy, 0 = unhealthy
        except:
            metrics["database_response_time_ms"] = -1
            metrics["database_status"] = 0
        
        # Redis metrics
        try:
            redis_healthy = await redis_client.ping()
            metrics["redis_status"] = 1 if redis_healthy else 0
            metrics["redis_memory_mb"] = await redis_client.get_memory_usage()
            metrics["redis_clients"] = await redis_client.get_client_count()
        except:
            metrics["redis_status"] = 0
            metrics["redis_memory_mb"] = -1
            metrics["redis_clients"] = -1
        
        # System metrics
        metrics["cpu_percent"] = psutil.cpu_percent()
        metrics["memory_percent"] = psutil.virtual_memory().percent
        metrics["disk_percent"] = psutil.disk_usage('/').percent
        
        # Business metrics
        try:
            metrics["active_providers"] = await redis_client.get_active_providers_count()
            metrics["active_followers"] = await redis_client.get_active_followers_count()
            metrics["signals_today"] = await redis_client.get_daily_signal_count()
        except:
            metrics["active_providers"] = -1
            metrics["active_followers"] = -1
            metrics["signals_today"] = -1
        
        return {
            "metrics": metrics,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Health metrics error: {e}")
        raise HTTPException(status_code=500, detail="Failed to get health metrics")

async def get_database_response_time():
    """Get database response time in milliseconds"""
    start_time = datetime.utcnow()
    await database.fetch_one("SELECT 1")
    end_time = datetime.utcnow()
    return round((end_time - start_time).total_seconds() * 1000, 2)

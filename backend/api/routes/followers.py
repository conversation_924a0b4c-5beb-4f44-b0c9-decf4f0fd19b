"""
Followers API Routes
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from fastapi.security import H<PERSON><PERSON>uthorizationCredentials, HTT<PERSON><PERSON>earer
from typing import List, Optional
from datetime import datetime
import logging
import uuid

from ..models.schemas import (
    FollowerResponse, FollowerUpdate, SubscriptionCreate, SubscriptionResponse,
    MT5AccountCreate, MT5AccountResponse, MT5AccountUpdate
)
from ..models.database import database
from ..middleware.auth import get_current_user, require_follower, verify_token
from ..services.redis_client import RedisClient

logger = logging.getLogger(__name__)
router = APIRouter()
security = HTTPBearer()
redis_client = RedisClient()

@router.get("/", response_model=List[FollowerResponse])
async def list_followers(
    limit: int = Query(50, ge=1, le=100),
    offset: int = Query(0, ge=0),
    current_user: dict = Depends(get_current_user)
):
    """List followers (admin only)"""
    try:
        if current_user.get("role") != "admin":
            raise HTTPException(status_code=403, detail="Admin access required")
        
        query = """
        SELECT f.id, f.user_id, f.follower_id, f.balance, f.max_risk_per_trade,
               f.max_daily_loss, f.auto_copy_enabled, f.copy_ratio, f.max_lot_size,
               f.created_at, f.updated_at, u.email, u.first_name, u.last_name
        FROM followers f
        JOIN users u ON f.user_id = u.id
        ORDER BY f.created_at DESC
        LIMIT :limit OFFSET :offset
        """
        
        followers = await database.fetch_all(query, {"limit": limit, "offset": offset})
        
        return [FollowerResponse(**follower) for follower in followers]
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"List followers error: {e}")
        raise HTTPException(status_code=500, detail="Failed to list followers")

@router.get("/{follower_id}", response_model=FollowerResponse)
async def get_follower(
    follower_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Get follower details"""
    try:
        # Check permissions
        if (current_user.get("role") != "admin" and 
            current_user.get("follower_id") != follower_id):
            raise HTTPException(status_code=403, detail="Access denied")
        
        query = """
        SELECT f.id, f.user_id, f.follower_id, f.balance, f.max_risk_per_trade,
               f.max_daily_loss, f.auto_copy_enabled, f.copy_ratio, f.max_lot_size,
               f.created_at, f.updated_at, u.email, u.first_name, u.last_name
        FROM followers f
        JOIN users u ON f.user_id = u.id
        WHERE f.follower_id = :follower_id
        """
        
        follower = await database.fetch_one(query, {"follower_id": follower_id})
        
        if not follower:
            raise HTTPException(status_code=404, detail="Follower not found")
        
        return FollowerResponse(**follower)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get follower error: {e}")
        raise HTTPException(status_code=500, detail="Failed to get follower")

@router.put("/{follower_id}", response_model=FollowerResponse)
async def update_follower(
    follower_id: str,
    follower_update: FollowerUpdate,
    current_user: dict = Depends(get_current_user)
):
    """Update follower settings"""
    try:
        # Check permissions
        if (current_user.get("role") != "admin" and 
            current_user.get("follower_id") != follower_id):
            raise HTTPException(status_code=403, detail="Access denied")
        
        # Build update query
        update_fields = []
        values = {"follower_id": follower_id}
        
        for field, value in follower_update.dict(exclude_unset=True).items():
            if value is not None:
                update_fields.append(f"{field} = :{field}")
                values[field] = value
        
        if not update_fields:
            raise HTTPException(status_code=400, detail="No fields to update")
        
        values["updated_at"] = datetime.utcnow()
        update_fields.append("updated_at = :updated_at")
        
        query = f"""
        UPDATE followers 
        SET {', '.join(update_fields)}
        WHERE follower_id = :follower_id
        """
        
        await database.execute(query, values)
        
        # Return updated follower
        return await get_follower(follower_id, current_user)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Update follower error: {e}")
        raise HTTPException(status_code=500, detail="Failed to update follower")

@router.post("/register")
async def register_follower(
    account_data: MT5AccountCreate,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """Register follower with MT5 account info (called by Follower EA)"""
    try:
        user_data = await verify_token(credentials.credentials)
        if not user_data:
            raise HTTPException(status_code=401, detail="Invalid token")
        
        await require_follower(user_data)
        
        # Check if already registered
        query = """
        SELECT id FROM mt5_accounts 
        WHERE user_id = :user_id AND account_type = 'follower'
        """
        
        existing = await database.fetch_one(query, {"user_id": user_data["user_id"]})
        
        if existing:
            raise HTTPException(status_code=409, detail="Follower already registered")
        
        # Create MT5 account record
        account_id = str(uuid.uuid4())
        
        insert_query = """
        INSERT INTO mt5_accounts (
            id, user_id, account_number, broker, server, balance, equity,
            currency, leverage, account_type, created_at
        ) VALUES (
            :id, :user_id, :account_number, :broker, :server, :balance, :equity,
            :currency, :leverage, 'follower', :created_at
        )
        """
        
        await database.execute(insert_query, {
            "id": account_id,
            "user_id": user_data["user_id"],
            "account_number": account_data.account_number,
            "broker": account_data.broker,
            "server": account_data.server,
            "balance": account_data.balance,
            "equity": account_data.equity,
            "currency": account_data.currency,
            "leverage": account_data.leverage,
            "created_at": datetime.utcnow()
        })
        
        return {
            "status": "success",
            "message": "Follower registered successfully",
            "account_id": account_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Follower registration error: {e}")
        raise HTTPException(status_code=500, detail="Registration failed")

@router.post("/heartbeat")
async def follower_heartbeat(
    heartbeat_data: dict,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """Follower heartbeat (called by Follower EA)"""
    try:
        user_data = await verify_token(credentials.credentials)
        if not user_data:
            raise HTTPException(status_code=401, detail="Invalid token")
        
        await require_follower(user_data)
        
        # Update client heartbeat in Redis
        client_id = f"follower_{user_data['follower_id']}"
        await redis_client.update_client_heartbeat(client_id)
        
        # Update account info if provided
        if "balance" in heartbeat_data:
            # Could update MT5 account info in database here
            pass
        
        return {"status": "success", "timestamp": datetime.utcnow().isoformat()}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Follower heartbeat error: {e}")
        raise HTTPException(status_code=500, detail="Heartbeat failed")

@router.get("/{follower_id}/subscriptions", response_model=List[SubscriptionResponse])
async def get_follower_subscriptions(
    follower_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Get follower's subscriptions"""
    try:
        # Check permissions
        if (current_user.get("role") != "admin" and 
            current_user.get("follower_id") != follower_id):
            raise HTTPException(status_code=403, detail="Access denied")
        
        query = """
        SELECT s.id, s.provider_id, s.follower_id, s.copy_ratio, s.max_lot_size,
               s.is_active, s.subscribed_at, s.unsubscribed_at,
               p.strategy_name, p.description, p.risk_level
        FROM subscriptions s
        JOIN providers p ON s.provider_id = p.provider_id
        WHERE s.follower_id = :follower_id
        ORDER BY s.subscribed_at DESC
        """
        
        subscriptions = await database.fetch_all(query, {"follower_id": follower_id})
        
        return [SubscriptionResponse(**sub) for sub in subscriptions]
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get subscriptions error: {e}")
        raise HTTPException(status_code=500, detail="Failed to get subscriptions")

@router.post("/{follower_id}/subscriptions", response_model=SubscriptionResponse)
async def create_subscription(
    follower_id: str,
    subscription_data: SubscriptionCreate,
    current_user: dict = Depends(get_current_user)
):
    """Create new subscription"""
    try:
        # Check permissions
        if (current_user.get("role") != "admin" and 
            current_user.get("follower_id") != follower_id):
            raise HTTPException(status_code=403, detail="Access denied")
        
        # Check if subscription already exists
        query = """
        SELECT id FROM subscriptions 
        WHERE follower_id = :follower_id AND provider_id = :provider_id AND is_active = true
        """
        
        existing = await database.fetch_one(query, {
            "follower_id": follower_id,
            "provider_id": subscription_data.provider_id
        })
        
        if existing:
            raise HTTPException(status_code=409, detail="Subscription already exists")
        
        # Create subscription
        subscription_id = str(uuid.uuid4())
        
        insert_query = """
        INSERT INTO subscriptions (
            id, provider_id, follower_id, copy_ratio, max_lot_size,
            is_active, subscribed_at
        ) VALUES (
            :id, :provider_id, :follower_id, :copy_ratio, :max_lot_size,
            true, :subscribed_at
        )
        """
        
        await database.execute(insert_query, {
            "id": subscription_id,
            "provider_id": subscription_data.provider_id,
            "follower_id": follower_id,
            "copy_ratio": subscription_data.copy_ratio,
            "max_lot_size": subscription_data.max_lot_size,
            "subscribed_at": datetime.utcnow()
        })
        
        # Return created subscription
        return await get_subscription(subscription_id)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Create subscription error: {e}")
        raise HTTPException(status_code=500, detail="Failed to create subscription")

@router.put("/{follower_id}/subscriptions/{subscription_id}")
async def update_subscription(
    follower_id: str,
    subscription_id: str,
    subscription_update: dict,
    current_user: dict = Depends(get_current_user)
):
    """Update subscription settings"""
    try:
        # Check permissions
        if (current_user.get("role") != "admin" and 
            current_user.get("follower_id") != follower_id):
            raise HTTPException(status_code=403, detail="Access denied")
        
        # Build update query
        update_fields = []
        values = {"subscription_id": subscription_id, "follower_id": follower_id}
        
        for field, value in subscription_update.items():
            if field in ["copy_ratio", "max_lot_size", "is_active"]:
                update_fields.append(f"{field} = :{field}")
                values[field] = value
        
        if not update_fields:
            raise HTTPException(status_code=400, detail="No valid fields to update")
        
        query = f"""
        UPDATE subscriptions 
        SET {', '.join(update_fields)}
        WHERE id = :subscription_id AND follower_id = :follower_id
        """
        
        result = await database.execute(query, values)
        
        if result == 0:
            raise HTTPException(status_code=404, detail="Subscription not found")
        
        return {"status": "success", "message": "Subscription updated"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Update subscription error: {e}")
        raise HTTPException(status_code=500, detail="Failed to update subscription")

async def get_subscription(subscription_id: str):
    """Helper function to get subscription details"""
    query = """
    SELECT s.id, s.provider_id, s.follower_id, s.copy_ratio, s.max_lot_size,
           s.is_active, s.subscribed_at, s.unsubscribed_at,
           p.strategy_name, p.description, p.risk_level
    FROM subscriptions s
    JOIN providers p ON s.provider_id = p.provider_id
    WHERE s.id = :subscription_id
    """
    
    subscription = await database.fetch_one(query, {"subscription_id": subscription_id})
    
    if not subscription:
        raise HTTPException(status_code=404, detail="Subscription not found")
    
    return SubscriptionResponse(**subscription)

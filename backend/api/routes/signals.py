"""
Signals API Routes
Handles signal creation, retrieval, and management
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from fastapi.security import HTTPAuthorizationCredentials, HTT<PERSON><PERSON>earer
from typing import List, Optional
from datetime import datetime, timedelta
import logging

from ..models.schemas import Signal<PERSON><PERSON>, SignalResponse, SignalUpdate
from ..models.database import database
from ..services.signal_processor import SignalProcessor
from ..middleware.auth import verify_token, require_provider, require_admin
from ..services.redis_client import RedisClient
from ..services.partial_close_service import partial_close_service
from ..services.pending_order_service import (
    pending_order_service,
    duplicate_prevention,
    signal_validation
)

logger = logging.getLogger(__name__)
router = APIRouter()
security = HTTPBearer()

# Initialize services
signal_processor = SignalProcessor()
redis_client = RedisClient()

@router.post("/", response_model=dict)
async def create_signal(
    signal_data: SignalCreate,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """Create new trading signal (Provider only)"""
    try:
        # Verify provider authentication
        user_data = await verify_token(credentials.credentials)
        await require_provider(user_data)
        
        # Verify provider owns the signal
        if signal_data.provider_id != user_data.get("provider_id"):
            raise HTTPException(status_code=403, detail="Cannot create signal for other provider")
        
        # Get signal dictionary
        signal_dict = signal_data.dict()

        # Validate signal based on type
        if signal_dict.get("action") == "pending":
            validation_result = await signal_validation.validate_pending_order_signal(signal_dict)
        else:
            validation_result = await signal_validation.validate_position_signal(signal_dict)

        if not validation_result["is_valid"]:
            raise HTTPException(status_code=400, detail=f"Signal validation failed: {validation_result['errors']}")

        # Check for duplicates
        signal_hash = duplicate_prevention.generate_signal_hash(
            signal_dict["provider_id"],
            signal_dict["symbol"],
            signal_dict["action"],
            signal_dict.get("original_ticket", 0),
            datetime.utcnow()
        )

        if await duplicate_prevention.is_signal_duplicate(signal_hash):
            logger.warning(f"Duplicate signal detected: {signal_hash}")
            raise HTTPException(status_code=409, detail="Duplicate signal")

        # Process signal based on type
        if signal_dict.get("is_partial_close", False):
            result = await partial_close_service.process_partial_close_signal(signal_dict)
        elif signal_dict.get("action") == "pending":
            # Track pending order
            if signal_dict.get("original_ticket"):
                await pending_order_service.track_pending_order(
                    signal_dict["provider_id"],
                    signal_dict["original_ticket"],
                    signal_dict.get("signal_id", "")
                )
            result = await signal_processor.process_signal(signal_dict)
        else:
            # Check if this position came from a pending order
            if signal_dict.get("action") == "open" and signal_dict.get("original_ticket"):
                if await pending_order_service.is_position_from_pending_order(
                    signal_dict["provider_id"],
                    signal_dict["original_ticket"]
                ):
                    logger.info(f"Position {signal_dict['original_ticket']} came from pending order, skipping duplicate")
                    return {
                        "success": True,
                        "signal_id": signal_dict.get("signal_id", ""),
                        "status": "skipped_duplicate",
                        "message": "Position from pending order already processed"
                    }

            # Process regular signal
            result = await signal_processor.process_signal(signal_dict)

        # Mark signal as processed to prevent duplicates
        if result.get("success"):
            await duplicate_prevention.mark_signal_as_processed(signal_hash, result["signal_id"])

        return {
            "success": True,
            "signal_id": result["signal_id"],
            "status": result.get("status", "processed"),
            "message": "Signal created successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Signal creation error: {e}")
        raise HTTPException(status_code=500, detail="Failed to create signal")

@router.get("/", response_model=List[SignalResponse])
async def get_signals(
    provider_id: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    symbol: Optional[str] = Query(None),
    action: Optional[str] = Query(None),
    limit: int = Query(50, le=100),
    offset: int = Query(0, ge=0),
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """Get signals with filtering options"""
    try:
        user_data = await verify_token(credentials.credentials)
        
        # Build query based on user role
        query_conditions = []
        query_params = {"limit": limit, "offset": offset}
        
        # Provider can only see their own signals
        if user_data.get("user_type") == "provider":
            query_conditions.append("provider_id = :user_provider_id")
            query_params["user_provider_id"] = user_data.get("provider_id")
        
        # Follower can see signals from subscribed providers
        elif user_data.get("user_type") == "follower":
            subscribed_providers = await get_subscribed_providers(user_data.get("follower_id"))
            if not subscribed_providers:
                return []
            
            provider_placeholders = ",".join([f":provider_{i}" for i in range(len(subscribed_providers))])
            query_conditions.append(f"provider_id IN ({provider_placeholders})")
            
            for i, provider in enumerate(subscribed_providers):
                query_params[f"provider_{i}"] = provider
        
        # Apply filters
        if provider_id:
            query_conditions.append("provider_id = :provider_id")
            query_params["provider_id"] = provider_id
        
        if status:
            query_conditions.append("status = :status")
            query_params["status"] = status
        
        if symbol:
            query_conditions.append("symbol = :symbol")
            query_params["symbol"] = symbol
        
        if action:
            query_conditions.append("action = :action")
            query_params["action"] = action
        
        # Build final query
        where_clause = " AND ".join(query_conditions) if query_conditions else "1=1"
        
        query = f"""
        SELECT signal_id, provider_id, action, symbol, type, volume,
               open_price, stop_loss, take_profit, comment, original_ticket,
               status, created_at, processed_at
        FROM signals
        WHERE {where_clause}
        ORDER BY created_at DESC
        LIMIT :limit OFFSET :offset
        """
        
        signals = await database.fetch_all(query, query_params)
        
        return [SignalResponse(**signal) for signal in signals]
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get signals error: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve signals")

@router.get("/{signal_id}", response_model=SignalResponse)
async def get_signal(
    signal_id: str,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """Get specific signal by ID"""
    try:
        user_data = await verify_token(credentials.credentials)
        
        # Get signal from cache first
        signal = await redis_client.get_signal(signal_id)
        
        if not signal:
            # Fallback to database
            query = """
            SELECT signal_id, provider_id, action, symbol, type, volume,
                   open_price, stop_loss, take_profit, comment, original_ticket,
                   status, created_at, processed_at
            FROM signals
            WHERE signal_id = :signal_id
            """
            
            signal = await database.fetch_one(query, {"signal_id": signal_id})
        
        if not signal:
            raise HTTPException(status_code=404, detail="Signal not found")
        
        # Check access permissions
        if user_data.get("user_type") == "provider":
            if signal["provider_id"] != user_data.get("provider_id"):
                raise HTTPException(status_code=403, detail="Access denied")
        
        elif user_data.get("user_type") == "follower":
            subscribed_providers = await get_subscribed_providers(user_data.get("follower_id"))
            if signal["provider_id"] not in subscribed_providers:
                raise HTTPException(status_code=403, detail="Access denied")
        
        return SignalResponse(**signal)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get signal error: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve signal")

@router.put("/{signal_id}", response_model=dict)
async def update_signal(
    signal_id: str,
    signal_update: SignalUpdate,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """Update signal (Provider only)"""
    try:
        user_data = await verify_token(credentials.credentials)
        await require_provider(user_data)
        
        # Get existing signal
        query = """
        SELECT signal_id, provider_id, status
        FROM signals
        WHERE signal_id = :signal_id
        """
        
        signal = await database.fetch_one(query, {"signal_id": signal_id})
        
        if not signal:
            raise HTTPException(status_code=404, detail="Signal not found")
        
        # Check ownership
        if signal["provider_id"] != user_data.get("provider_id"):
            raise HTTPException(status_code=403, detail="Cannot update other provider's signal")
        
        # Check if signal can be updated
        if signal["status"] in ["completed", "cancelled"]:
            raise HTTPException(status_code=400, detail="Cannot update completed or cancelled signal")
        
        # Update signal
        update_fields = []
        update_params = {"signal_id": signal_id}
        
        if signal_update.stop_loss is not None:
            update_fields.append("stop_loss = :stop_loss")
            update_params["stop_loss"] = signal_update.stop_loss
        
        if signal_update.take_profit is not None:
            update_fields.append("take_profit = :take_profit")
            update_params["take_profit"] = signal_update.take_profit
        
        if signal_update.comment is not None:
            update_fields.append("comment = :comment")
            update_params["comment"] = signal_update.comment
        
        if signal_update.status is not None:
            update_fields.append("status = :status")
            update_params["status"] = signal_update.status
        
        if not update_fields:
            raise HTTPException(status_code=400, detail="No fields to update")
        
        update_query = f"""
        UPDATE signals
        SET {', '.join(update_fields)}, processed_at = :processed_at
        WHERE signal_id = :signal_id
        """
        
        update_params["processed_at"] = datetime.utcnow()
        
        await database.execute(update_query, update_params)
        
        # Update cache
        await redis_client.update_signal_status(signal_id, update_params.get("status", signal["status"]))
        
        return {
            "success": True,
            "message": "Signal updated successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Update signal error: {e}")
        raise HTTPException(status_code=500, detail="Failed to update signal")

@router.delete("/{signal_id}", response_model=dict)
async def cancel_signal(
    signal_id: str,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """Cancel signal (Provider only)"""
    try:
        user_data = await verify_token(credentials.credentials)
        await require_provider(user_data)
        
        # Get existing signal
        query = """
        SELECT signal_id, provider_id, status
        FROM signals
        WHERE signal_id = :signal_id
        """
        
        signal = await database.fetch_one(query, {"signal_id": signal_id})
        
        if not signal:
            raise HTTPException(status_code=404, detail="Signal not found")
        
        # Check ownership
        if signal["provider_id"] != user_data.get("provider_id"):
            raise HTTPException(status_code=403, detail="Cannot cancel other provider's signal")
        
        # Check if signal can be cancelled
        if signal["status"] in ["completed", "cancelled"]:
            raise HTTPException(status_code=400, detail="Signal already completed or cancelled")
        
        # Cancel signal
        cancel_query = """
        UPDATE signals
        SET status = 'cancelled', processed_at = :processed_at
        WHERE signal_id = :signal_id
        """
        
        await database.execute(cancel_query, {
            "signal_id": signal_id,
            "processed_at": datetime.utcnow()
        })
        
        # Update cache
        await redis_client.update_signal_status(signal_id, "cancelled")
        
        # Broadcast cancellation to followers
        cancel_message = {
            "type": "signal_cancelled",
            "signal_id": signal_id,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        from ..services.websocket_manager import WebSocketManager
        websocket_manager = WebSocketManager()
        await websocket_manager.send_to_followers(signal["provider_id"], cancel_message)
        
        return {
            "success": True,
            "message": "Signal cancelled successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Cancel signal error: {e}")
        raise HTTPException(status_code=500, detail="Failed to cancel signal")

@router.get("/stats/summary", response_model=dict)
async def get_signal_stats(
    provider_id: Optional[str] = Query(None),
    days: int = Query(7, ge=1, le=90),
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """Get signal statistics"""
    try:
        user_data = await verify_token(credentials.credentials)
        
        # Determine provider_id based on user type
        if user_data.get("user_type") == "provider":
            provider_id = user_data.get("provider_id")
        elif not provider_id and user_data.get("role") != "admin":
            raise HTTPException(status_code=400, detail="Provider ID required")
        
        # Calculate date range
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # Build query
        query_params = {"start_date": start_date, "end_date": end_date}
        where_clause = "created_at BETWEEN :start_date AND :end_date"
        
        if provider_id:
            where_clause += " AND provider_id = :provider_id"
            query_params["provider_id"] = provider_id
        
        # Get statistics
        stats_query = f"""
        SELECT 
            COUNT(*) as total_signals,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_signals,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_signals,
            COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_signals,
            COUNT(CASE WHEN action = 'open' THEN 1 END) as open_signals,
            COUNT(CASE WHEN action = 'close' THEN 1 END) as close_signals,
            COUNT(DISTINCT symbol) as unique_symbols,
            AVG(volume) as avg_volume
        FROM signals
        WHERE {where_clause}
        """
        
        stats = await database.fetch_one(stats_query, query_params)
        
        return {
            "period_days": days,
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "provider_id": provider_id,
            "statistics": dict(stats)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Signal stats error: {e}")
        raise HTTPException(status_code=500, detail="Failed to get signal statistics")

async def get_subscribed_providers(follower_id: str) -> List[str]:
    """Get list of providers that follower is subscribed to"""
    query = """
    SELECT provider_id
    FROM subscriptions
    WHERE follower_id = :follower_id AND is_active = true
    """
    
    subscriptions = await database.fetch_all(query, {"follower_id": follower_id})
    return [sub["provider_id"] for sub in subscriptions]

"""
Authentication API Routes
"""

from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.security import HTT<PERSON>uthorizationCredentials, HTTPBearer
from datetime import timedelta
import uuid
import logging

from ..models.schemas import (
    LoginRequest, LoginResponse, UserCreate, UserResponse, 
    APIKeyCreate, APIKeyResponse
)
from ..models.database import database
from ..middleware.auth import auth_manager, get_current_user
from ..config import settings

logger = logging.getLogger(__name__)
router = APIRouter()
security = HTTPBearer()

@router.post("/login", response_model=LoginResponse)
async def login(login_data: LoginRequest):
    """User login"""
    try:
        user_data = await auth_manager.authenticate_user(
            login_data.email, 
            login_data.password
        )
        
        if not user_data:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect email or password"
            )
        
        # Create access token
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = auth_manager.create_access_token(
            data={"user_id": user_data["user_id"]},
            expires_delta=access_token_expires
        )
        
        # Get user details
        user_query = """
        SELECT id, email, first_name, last_name, user_type, is_active, is_verified, created_at
        FROM users WHERE id = :user_id
        """
        user = await database.fetch_one(user_query, {"user_id": user_data["user_id"]})
        
        return LoginResponse(
            access_token=access_token,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            user=UserResponse(**user)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(status_code=500, detail="Login failed")

@router.post("/register", response_model=UserResponse)
async def register(user_data: UserCreate):
    """User registration"""
    try:
        # Check if user already exists
        existing_user = await database.fetch_one(
            "SELECT id FROM users WHERE email = :email",
            {"email": user_data.email}
        )
        
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )
        
        # Hash password
        password_hash = auth_manager.get_password_hash(user_data.password)
        
        # Create user
        user_id = str(uuid.uuid4())
        query = """
        INSERT INTO users (id, email, password_hash, first_name, last_name, user_type)
        VALUES (:id, :email, :password_hash, :first_name, :last_name, :user_type)
        RETURNING id, email, first_name, last_name, user_type, is_active, is_verified, created_at
        """
        
        user = await database.fetch_one(query, {
            "id": user_id,
            "email": user_data.email,
            "password_hash": password_hash,
            "first_name": user_data.first_name,
            "last_name": user_data.last_name,
            "user_type": user_data.user_type
        })
        
        # Create provider/follower profiles based on user type
        if user_data.user_type in ["provider", "both"]:
            await create_provider_profile(user_id)
        
        if user_data.user_type in ["follower", "both"]:
            await create_follower_profile(user_id)
        
        return UserResponse(**user)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Registration error: {e}")
        raise HTTPException(status_code=500, detail="Registration failed")

@router.post("/refresh")
async def refresh_token(
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """Refresh access token"""
    try:
        payload = auth_manager.verify_token(credentials.credentials)
        
        if not payload or payload.get("type") != "refresh":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token"
            )
        
        user_id = payload.get("user_id")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token payload"
            )
        
        # Create new access token
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = auth_manager.create_access_token(
            data={"user_id": user_id},
            expires_delta=access_token_expires
        )
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Token refresh error: {e}")
        raise HTTPException(status_code=500, detail="Token refresh failed")

@router.post("/api-keys", response_model=APIKeyResponse)
async def create_api_key(
    api_key_data: APIKeyCreate,
    current_user: dict = Depends(get_current_user)
):
    """Create API key"""
    try:
        api_key = await auth_manager.create_api_key(
            current_user["user_id"],
            api_key_data.key_name,
            api_key_data.permissions
        )
        
        # Get the created API key record
        query = """
        SELECT id, key_name, api_key, permissions, is_active, expires_at, created_at
        FROM api_keys
        WHERE api_key = :api_key
        """
        
        key_record = await database.fetch_one(query, {"api_key": api_key})
        
        return APIKeyResponse(**key_record)
        
    except Exception as e:
        logger.error(f"API key creation error: {e}")
        raise HTTPException(status_code=500, detail="Failed to create API key")

@router.get("/api-keys")
async def list_api_keys(current_user: dict = Depends(get_current_user)):
    """List user's API keys"""
    try:
        query = """
        SELECT id, key_name, api_key, permissions, is_active, expires_at, last_used, created_at
        FROM api_keys
        WHERE user_id = :user_id
        ORDER BY created_at DESC
        """
        
        keys = await database.fetch_all(query, {"user_id": current_user["user_id"]})
        
        # Mask API keys for security
        for key in keys:
            key["api_key"] = key["api_key"][:8] + "..." + key["api_key"][-4:]
        
        return keys
        
    except Exception as e:
        logger.error(f"API keys list error: {e}")
        raise HTTPException(status_code=500, detail="Failed to list API keys")

@router.delete("/api-keys/{key_id}")
async def revoke_api_key(
    key_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Revoke API key"""
    try:
        # Check if key belongs to user
        check_query = """
        SELECT id FROM api_keys 
        WHERE id = :key_id AND user_id = :user_id
        """
        
        key_exists = await database.fetch_one(check_query, {
            "key_id": key_id,
            "user_id": current_user["user_id"]
        })
        
        if not key_exists:
            raise HTTPException(status_code=404, detail="API key not found")
        
        # Revoke key
        revoke_query = """
        UPDATE api_keys 
        SET is_active = false 
        WHERE id = :key_id
        """
        
        await database.execute(revoke_query, {"key_id": key_id})
        
        return {"message": "API key revoked successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"API key revocation error: {e}")
        raise HTTPException(status_code=500, detail="Failed to revoke API key")

@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: dict = Depends(get_current_user)):
    """Get current user information"""
    try:
        query = """
        SELECT id, email, first_name, last_name, user_type, is_active, is_verified, created_at, updated_at
        FROM users WHERE id = :user_id
        """
        
        user = await database.fetch_one(query, {"user_id": current_user["user_id"]})
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        return UserResponse(**user)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get user info error: {e}")
        raise HTTPException(status_code=500, detail="Failed to get user information")

@router.post("/logout")
async def logout(current_user: dict = Depends(get_current_user)):
    """User logout (client-side token invalidation)"""
    # In a stateless JWT system, logout is typically handled client-side
    # by removing the token. For enhanced security, you could implement
    # a token blacklist in Redis.
    
    return {"message": "Logged out successfully"}

async def create_provider_profile(user_id: str):
    """Create provider profile for user"""
    provider_id = f"provider_{uuid.uuid4().hex[:8]}"
    
    query = """
    INSERT INTO providers (user_id, provider_id, display_name)
    VALUES (:user_id, :provider_id, :display_name)
    """
    
    await database.execute(query, {
        "user_id": user_id,
        "provider_id": provider_id,
        "display_name": f"Provider {provider_id}"
    })

async def create_follower_profile(user_id: str):
    """Create follower profile for user"""
    follower_id = f"follower_{uuid.uuid4().hex[:8]}"
    
    query = """
    INSERT INTO followers (user_id, follower_id)
    VALUES (:user_id, :follower_id)
    """
    
    await database.execute(query, {
        "user_id": user_id,
        "follower_id": follower_id
    })

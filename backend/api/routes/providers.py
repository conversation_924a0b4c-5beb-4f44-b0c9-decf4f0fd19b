"""
Providers API Routes
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from fastapi.security import HTT<PERSON>uthorizationCredentials, HTTPBearer
from typing import List, Optional
from datetime import datetime
import logging

from ..models.schemas import (
    ProviderResponse, ProviderUpdate, PerformanceMetricsResponse,
    MT5AccountCreate, MT5AccountResponse, MT5AccountUpdate
)
from ..models.database import database
from ..middleware.auth import get_current_user, require_provider
from ..services.redis_client import RedisClient

logger = logging.getLogger(__name__)
router = APIRouter()
security = HTTPBearer()
redis_client = RedisClient()

@router.get("/", response_model=List[ProviderResponse])
async def list_providers(
    is_verified: Optional[bool] = Query(None),
    is_active: Optional[bool] = Query(True),
    limit: int = Query(50, le=100),
    offset: int = Query(0, ge=0)
):
    """List all providers"""
    try:
        conditions = []
        params = {"limit": limit, "offset": offset}
        
        if is_verified is not None:
            conditions.append("is_verified = :is_verified")
            params["is_verified"] = is_verified
        
        if is_active is not None:
            conditions.append("is_active = :is_active")
            params["is_active"] = is_active
        
        where_clause = " AND ".join(conditions) if conditions else "1=1"
        
        query = f"""
        SELECT id, provider_id, user_id, display_name, description, 
               subscription_fee, max_followers, is_verified, is_active,
               min_balance_required, risk_level, created_at, updated_at
        FROM providers
        WHERE {where_clause}
        ORDER BY created_at DESC
        LIMIT :limit OFFSET :offset
        """
        
        providers = await database.fetch_all(query, params)
        return [ProviderResponse(**provider) for provider in providers]
        
    except Exception as e:
        logger.error(f"List providers error: {e}")
        raise HTTPException(status_code=500, detail="Failed to list providers")

@router.get("/{provider_id}", response_model=ProviderResponse)
async def get_provider(provider_id: str):
    """Get provider by ID"""
    try:
        query = """
        SELECT id, provider_id, user_id, display_name, description, 
               subscription_fee, max_followers, is_verified, is_active,
               min_balance_required, risk_level, created_at, updated_at
        FROM providers
        WHERE provider_id = :provider_id
        """
        
        provider = await database.fetch_one(query, {"provider_id": provider_id})
        
        if not provider:
            raise HTTPException(status_code=404, detail="Provider not found")
        
        return ProviderResponse(**provider)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get provider error: {e}")
        raise HTTPException(status_code=500, detail="Failed to get provider")

@router.put("/{provider_id}", response_model=ProviderResponse)
async def update_provider(
    provider_id: str,
    provider_update: ProviderUpdate,
    current_user: dict = Depends(get_current_user)
):
    """Update provider profile"""
    try:
        await require_provider(current_user)
        
        # Check if user owns this provider
        if current_user.get("provider_id") != provider_id:
            raise HTTPException(status_code=403, detail="Cannot update other provider's profile")
        
        # Build update query
        update_fields = []
        update_params = {"provider_id": provider_id}
        
        if provider_update.display_name is not None:
            update_fields.append("display_name = :display_name")
            update_params["display_name"] = provider_update.display_name
        
        if provider_update.description is not None:
            update_fields.append("description = :description")
            update_params["description"] = provider_update.description
        
        if provider_update.subscription_fee is not None:
            update_fields.append("subscription_fee = :subscription_fee")
            update_params["subscription_fee"] = provider_update.subscription_fee
        
        if provider_update.max_followers is not None:
            update_fields.append("max_followers = :max_followers")
            update_params["max_followers"] = provider_update.max_followers
        
        if provider_update.min_balance_required is not None:
            update_fields.append("min_balance_required = :min_balance_required")
            update_params["min_balance_required"] = provider_update.min_balance_required
        
        if provider_update.risk_level is not None:
            update_fields.append("risk_level = :risk_level")
            update_params["risk_level"] = provider_update.risk_level
        
        if not update_fields:
            raise HTTPException(status_code=400, detail="No fields to update")
        
        update_query = f"""
        UPDATE providers
        SET {', '.join(update_fields)}, updated_at = NOW()
        WHERE provider_id = :provider_id
        RETURNING id, provider_id, user_id, display_name, description, 
                  subscription_fee, max_followers, is_verified, is_active,
                  min_balance_required, risk_level, created_at, updated_at
        """
        
        updated_provider = await database.fetch_one(update_query, update_params)
        
        return ProviderResponse(**updated_provider)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Update provider error: {e}")
        raise HTTPException(status_code=500, detail="Failed to update provider")

@router.post("/register")
async def register_provider(
    provider_data: dict,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """Register provider with MT5 account info (called by Provider EA)"""
    try:
        from ..middleware.auth import verify_token, require_provider
        
        user_data = await verify_token(credentials.credentials)
        if not user_data:
            raise HTTPException(status_code=401, detail="Invalid token")
        
        await require_provider(user_data)
        
        # Update provider with MT5 account info
        query = """
        UPDATE providers 
        SET updated_at = NOW()
        WHERE provider_id = :provider_id
        """
        
        await database.execute(query, {"provider_id": user_data["provider_id"]})
        
        # Register MT5 client in Redis
        client_id = f"provider_{user_data['provider_id']}"
        await redis_client.register_mt5_client(client_id, {
            **user_data,
            "account_number": provider_data.get("account_number"),
            "broker": provider_data.get("broker"),
            "server": provider_data.get("server"),
            "balance": provider_data.get("balance"),
            "equity": provider_data.get("equity")
        })
        
        return {"status": "success", "message": "Provider registered successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Provider registration error: {e}")
        raise HTTPException(status_code=500, detail="Provider registration failed")

@router.post("/heartbeat")
async def provider_heartbeat(
    heartbeat_data: dict,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """Provider heartbeat (called by Provider EA)"""
    try:
        from ..middleware.auth import verify_token, require_provider
        
        user_data = await verify_token(credentials.credentials)
        if not user_data:
            raise HTTPException(status_code=401, detail="Invalid token")
        
        await require_provider(user_data)
        
        # Update client heartbeat in Redis
        client_id = f"provider_{user_data['provider_id']}"
        await redis_client.update_client_heartbeat(client_id)
        
        # Update account info if provided
        if "balance" in heartbeat_data:
            # Could update MT5 account info in database here
            pass
        
        return {"status": "success", "timestamp": datetime.utcnow().isoformat()}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Provider heartbeat error: {e}")
        raise HTTPException(status_code=500, detail="Heartbeat failed")

@router.post("/status")
async def update_provider_status(
    status_data: dict,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """Update provider status (called by Provider EA)"""
    try:
        from ..middleware.auth import verify_token, require_provider
        
        user_data = await verify_token(credentials.credentials)
        if not user_data:
            raise HTTPException(status_code=401, detail="Invalid token")
        
        await require_provider(user_data)
        
        status = status_data.get("status", "online")
        
        # Update provider status
        if status == "offline":
            # Remove from Redis
            client_id = f"provider_{user_data['provider_id']}"
            await redis_client.delete_cache(f"mt5_client:{client_id}")
        
        return {"status": "success", "message": f"Status updated to {status}"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Provider status update error: {e}")
        raise HTTPException(status_code=500, detail="Status update failed")

@router.get("/{provider_id}/performance", response_model=List[PerformanceMetricsResponse])
async def get_provider_performance(
    provider_id: str,
    days: int = Query(30, ge=1, le=365)
):
    """Get provider performance metrics"""
    try:
        query = """
        SELECT id, provider_id, metric_date, total_profit, total_loss,
               total_trades, winning_trades, losing_trades, win_rate,
               max_drawdown, sharpe_ratio, profit_factor, created_at, updated_at
        FROM performance_metrics
        WHERE provider_id = :provider_id
        AND metric_date >= CURRENT_DATE - INTERVAL '%s days'
        ORDER BY metric_date DESC
        """ % days
        
        metrics = await database.fetch_all(query, {"provider_id": provider_id})
        
        return [PerformanceMetricsResponse(**metric) for metric in metrics]
        
    except Exception as e:
        logger.error(f"Get provider performance error: {e}")
        raise HTTPException(status_code=500, detail="Failed to get performance metrics")

@router.get("/{provider_id}/followers")
async def get_provider_followers(
    provider_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Get provider's followers (provider only)"""
    try:
        await require_provider(current_user)
        
        # Check if user owns this provider
        if current_user.get("provider_id") != provider_id:
            raise HTTPException(status_code=403, detail="Cannot view other provider's followers")
        
        query = """
        SELECT f.follower_id, f.balance, s.copy_ratio, s.is_active, s.subscribed_at
        FROM subscriptions s
        JOIN followers f ON s.follower_id = f.follower_id
        WHERE s.provider_id = :provider_id
        ORDER BY s.subscribed_at DESC
        """
        
        followers = await database.fetch_all(query, {"provider_id": provider_id})
        
        return {
            "provider_id": provider_id,
            "total_followers": len(followers),
            "followers": followers
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get provider followers error: {e}")
        raise HTTPException(status_code=500, detail="Failed to get followers")

@router.get("/{provider_id}/stats")
async def get_provider_stats(
    provider_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Get provider statistics"""
    try:
        await require_provider(current_user)
        
        # Check if user owns this provider or is admin
        if current_user.get("provider_id") != provider_id:
            # Allow admin access
            try:
                from ..middleware.auth import require_admin
                await require_admin(current_user)
            except HTTPException:
                raise HTTPException(status_code=403, detail="Access denied")
        
        # Get various statistics
        stats_queries = {
            "total_signals": "SELECT COUNT(*) FROM signals WHERE provider_id = :provider_id",
            "signals_today": "SELECT COUNT(*) FROM signals WHERE provider_id = :provider_id AND DATE(created_at) = CURRENT_DATE",
            "total_followers": "SELECT COUNT(*) FROM subscriptions WHERE provider_id = :provider_id AND is_active = true",
            "total_trades": "SELECT COUNT(*) FROM trades t JOIN signals s ON t.signal_id = s.signal_id WHERE s.provider_id = :provider_id"
        }
        
        stats = {}
        for stat_name, query in stats_queries.items():
            result = await database.fetch_one(query, {"provider_id": provider_id})
            stats[stat_name] = result["count"] if result else 0
        
        return {
            "provider_id": provider_id,
            "statistics": stats,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get provider stats error: {e}")
        raise HTTPException(status_code=500, detail="Failed to get provider statistics")

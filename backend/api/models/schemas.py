"""
Pydantic schemas for API models
"""

from pydantic import BaseModel, EmailStr, validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from decimal import Decimal
from enum import Enum

# Enums
class UserType(str, Enum):
    provider = "provider"
    follower = "follower"
    both = "both"

class SignalAction(str, Enum):
    open = "open"
    close = "close"
    modify = "modify"
    pending = "pending"  # For pending orders

class SignalType(str, Enum):
    buy = "buy"
    sell = "sell"

class SignalStatus(str, Enum):
    pending = "pending"
    distributed = "distributed"
    completed = "completed"
    cancelled = "cancelled"
    error = "error"

class TradeStatus(str, Enum):
    open = "open"
    closed = "closed"
    cancelled = "cancelled"

# Base schemas
class BaseSchema(BaseModel):
    class Config:
        orm_mode = True
        use_enum_values = True

# User schemas
class UserBase(BaseSchema):
    email: EmailStr
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    user_type: UserType

class UserCreate(UserBase):
    password: str
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters')
        return v

class UserUpdate(BaseSchema):
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    is_active: Optional[bool] = None

class UserResponse(UserBase):
    id: str
    is_active: bool
    is_verified: bool
    created_at: datetime
    updated_at: Optional[datetime] = None

# Provider schemas
class ProviderBase(BaseSchema):
    display_name: str
    description: Optional[str] = None
    subscription_fee: Optional[Decimal] = Decimal('0')
    max_followers: Optional[int] = 100
    min_balance_required: Optional[Decimal] = Decimal('0')
    risk_level: Optional[int] = 3

class ProviderCreate(ProviderBase):
    pass

class ProviderUpdate(BaseSchema):
    display_name: Optional[str] = None
    description: Optional[str] = None
    subscription_fee: Optional[Decimal] = None
    max_followers: Optional[int] = None
    min_balance_required: Optional[Decimal] = None
    risk_level: Optional[int] = None
    is_active: Optional[bool] = None

class ProviderResponse(ProviderBase):
    id: str
    provider_id: str
    user_id: str
    is_verified: bool
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime] = None

# Follower schemas
class FollowerBase(BaseSchema):
    balance: Optional[Decimal] = Decimal('0')
    max_risk_per_trade: Optional[Decimal] = Decimal('2.0')
    max_daily_loss: Optional[Decimal] = Decimal('500')
    auto_copy_enabled: Optional[bool] = True
    copy_ratio: Optional[Decimal] = Decimal('1.0')
    max_lot_size: Optional[Decimal] = Decimal('1.0')

class FollowerCreate(FollowerBase):
    pass

class FollowerUpdate(BaseSchema):
    balance: Optional[Decimal] = None
    max_risk_per_trade: Optional[Decimal] = None
    max_daily_loss: Optional[Decimal] = None
    auto_copy_enabled: Optional[bool] = None
    copy_ratio: Optional[Decimal] = None
    max_lot_size: Optional[Decimal] = None

class FollowerResponse(FollowerBase):
    id: str
    follower_id: str
    user_id: str
    created_at: datetime
    updated_at: Optional[datetime] = None

# MT5 Account schemas
class MT5AccountBase(BaseSchema):
    account_number: str
    account_name: Optional[str] = None
    broker_name: str
    server: str
    currency: Optional[str] = "USD"
    leverage: Optional[int] = 100

class MT5AccountCreate(MT5AccountBase):
    pass

class MT5AccountUpdate(BaseSchema):
    account_name: Optional[str] = None
    is_active: Optional[bool] = None
    balance: Optional[Decimal] = None
    equity: Optional[Decimal] = None
    margin: Optional[Decimal] = None
    free_margin: Optional[Decimal] = None

class MT5AccountResponse(MT5AccountBase):
    id: str
    user_id: str
    is_active: bool
    balance: Decimal
    equity: Decimal
    margin: Decimal
    free_margin: Decimal
    last_sync: Optional[datetime] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

# Signal schemas
class SignalBase(BaseSchema):
    provider_id: str
    action: SignalAction
    symbol: str
    type: Optional[SignalType] = None
    volume: Optional[Decimal] = None
    open_price: Optional[Decimal] = None
    stop_loss: Optional[Decimal] = None
    take_profit: Optional[Decimal] = None
    comment: Optional[str] = None
    original_ticket: Optional[int] = None
    # Partial close fields
    partial_close_volume: Optional[Decimal] = None
    remaining_volume: Optional[Decimal] = None
    is_partial_close: Optional[bool] = False
    parent_signal_id: Optional[str] = None

class SignalCreate(SignalBase):
    @validator('volume')
    def validate_volume(cls, v, values):
        if values.get('action') == 'open' and (v is None or v <= 0):
            raise ValueError('Volume is required for open signals and must be positive')
        return v

class SignalUpdate(BaseSchema):
    stop_loss: Optional[Decimal] = None
    take_profit: Optional[Decimal] = None
    comment: Optional[str] = None
    status: Optional[SignalStatus] = None

class SignalResponse(SignalBase):
    id: str
    signal_id: str
    status: SignalStatus
    created_at: datetime
    processed_at: Optional[datetime] = None

# Subscription schemas
class SubscriptionBase(BaseSchema):
    follower_id: str
    provider_id: str
    copy_ratio: Optional[Decimal] = Decimal('1.0')

class SubscriptionCreate(SubscriptionBase):
    expires_at: Optional[datetime] = None

class SubscriptionUpdate(BaseSchema):
    copy_ratio: Optional[Decimal] = None
    is_active: Optional[bool] = None
    expires_at: Optional[datetime] = None

class SubscriptionResponse(SubscriptionBase):
    id: str
    is_active: bool
    subscribed_at: datetime
    expires_at: Optional[datetime] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

# Trade schemas
class TradeBase(BaseSchema):
    signal_id: str
    follower_id: str
    symbol: str
    trade_type: SignalType
    lot_size: Decimal
    entry_price: Optional[Decimal] = None
    stop_loss: Optional[Decimal] = None
    take_profit: Optional[Decimal] = None
    # Partial close fields
    original_volume: Optional[Decimal] = None
    closed_volume: Optional[Decimal] = None
    remaining_volume: Optional[Decimal] = None
    is_partial_close: Optional[bool] = False
    parent_trade_id: Optional[str] = None

class TradeCreate(TradeBase):
    mt5_ticket: Optional[int] = None

class TradeUpdate(BaseSchema):
    exit_price: Optional[Decimal] = None
    profit_loss: Optional[Decimal] = None
    commission: Optional[Decimal] = None
    swap: Optional[Decimal] = None
    status: Optional[TradeStatus] = None
    closed_at: Optional[datetime] = None

class TradeResponse(TradeBase):
    id: str
    mt5_ticket: Optional[int] = None
    exit_price: Optional[Decimal] = None
    profit_loss: Optional[Decimal] = None
    commission: Optional[Decimal] = None
    swap: Optional[Decimal] = None
    status: TradeStatus
    opened_at: Optional[datetime] = None
    closed_at: Optional[datetime] = None
    created_at: datetime

# Execution Result schemas
class ExecutionResultBase(BaseSchema):
    execution_id: str
    signal_id: str
    follower_id: str
    success: bool
    ticket: Optional[int] = None
    executed_volume: Optional[Decimal] = None
    executed_price: Optional[Decimal] = None
    error_message: Optional[str] = None
    error_code: Optional[int] = None

class ExecutionResultCreate(ExecutionResultBase):
    execution_time: Optional[datetime] = None

class ExecutionResultResponse(ExecutionResultBase):
    id: str
    execution_time: datetime
    created_at: datetime

# Performance Metrics schemas
class PerformanceMetricsBase(BaseSchema):
    provider_id: str
    metric_date: datetime
    total_profit: Optional[Decimal] = Decimal('0')
    total_loss: Optional[Decimal] = Decimal('0')
    total_trades: Optional[int] = 0
    winning_trades: Optional[int] = 0
    losing_trades: Optional[int] = 0
    win_rate: Optional[Decimal] = Decimal('0')
    max_drawdown: Optional[Decimal] = Decimal('0')
    sharpe_ratio: Optional[Decimal] = Decimal('0')
    profit_factor: Optional[Decimal] = Decimal('0')

class PerformanceMetricsResponse(PerformanceMetricsBase):
    id: str
    created_at: datetime
    updated_at: Optional[datetime] = None

# API Key schemas
class APIKeyBase(BaseSchema):
    key_name: str
    permissions: Optional[Dict[str, Any]] = None
    expires_at: Optional[datetime] = None

class APIKeyCreate(APIKeyBase):
    pass

class APIKeyResponse(APIKeyBase):
    id: str
    api_key: str
    is_active: bool
    last_used: Optional[datetime] = None
    created_at: datetime

# Authentication schemas
class LoginRequest(BaseSchema):
    email: EmailStr
    password: str

class LoginResponse(BaseSchema):
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    user: UserResponse

class TokenData(BaseSchema):
    user_id: Optional[str] = None
    user_type: Optional[str] = None
    provider_id: Optional[str] = None
    follower_id: Optional[str] = None

# System schemas
class HealthCheck(BaseSchema):
    status: str
    timestamp: datetime
    version: Optional[str] = None

class SystemStats(BaseSchema):
    connected_clients: int
    active_providers: int
    active_followers: int
    signals_processed_today: int
    system_uptime: str

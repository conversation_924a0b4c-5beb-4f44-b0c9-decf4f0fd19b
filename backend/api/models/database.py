"""
Database configuration and models
"""

import databases
import sqlalchemy
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import os
from typing import Optional

# Database URL from environment
DATABASE_URL = os.getenv(
    "DATABASE_URL", 
    "postgresql://copytrading:password@localhost:5432/copytrading_db"
)

# Create database instance
database = databases.Database(DATABASE_URL)

# SQLAlchemy setup
engine = create_engine(DATABASE_URL)
metadata = MetaData()

# Tables definition
users_table = sqlalchemy.Table(
    "users",
    metadata,
    sqlalchemy.Column("id", sqlalchemy.String, primary_key=True),
    sqlalchemy.Column("email", sqlalchemy.String, unique=True, nullable=False),
    sqlalchemy.Column("password_hash", sqlalchemy.String, nullable=False),
    sqlalchemy.Column("first_name", sqlalchemy.String),
    sqlalchemy.Column("last_name", sqlalchemy.String),
    sqlalchemy.Column("user_type", sqlalchemy.Enum("provider", "follower", "both", name="user_type_enum")),
    sqlalchemy.Column("is_active", sqlalchemy.Boolean, default=True),
    sqlalchemy.Column("is_verified", sqlalchemy.Boolean, default=False),
    sqlalchemy.Column("created_at", sqlalchemy.DateTime),
    sqlalchemy.Column("updated_at", sqlalchemy.DateTime),
)

providers_table = sqlalchemy.Table(
    "providers",
    metadata,
    sqlalchemy.Column("id", sqlalchemy.String, primary_key=True),
    sqlalchemy.Column("user_id", sqlalchemy.String, sqlalchemy.ForeignKey("users.id")),
    sqlalchemy.Column("provider_id", sqlalchemy.String, unique=True, nullable=False),
    sqlalchemy.Column("display_name", sqlalchemy.String, nullable=False),
    sqlalchemy.Column("description", sqlalchemy.Text),
    sqlalchemy.Column("subscription_fee", sqlalchemy.Numeric(10, 2), default=0),
    sqlalchemy.Column("max_followers", sqlalchemy.Integer, default=100),
    sqlalchemy.Column("is_verified", sqlalchemy.Boolean, default=False),
    sqlalchemy.Column("is_active", sqlalchemy.Boolean, default=True),
    sqlalchemy.Column("min_balance_required", sqlalchemy.Numeric(10, 2), default=0),
    sqlalchemy.Column("risk_level", sqlalchemy.Integer, default=3),  # 1-5 scale
    sqlalchemy.Column("created_at", sqlalchemy.DateTime),
    sqlalchemy.Column("updated_at", sqlalchemy.DateTime),
)

followers_table = sqlalchemy.Table(
    "followers",
    metadata,
    sqlalchemy.Column("id", sqlalchemy.String, primary_key=True),
    sqlalchemy.Column("user_id", sqlalchemy.String, sqlalchemy.ForeignKey("users.id")),
    sqlalchemy.Column("follower_id", sqlalchemy.String, unique=True, nullable=False),
    sqlalchemy.Column("balance", sqlalchemy.Numeric(15, 2), default=0),
    sqlalchemy.Column("max_risk_per_trade", sqlalchemy.Numeric(5, 2), default=2.0),
    sqlalchemy.Column("max_daily_loss", sqlalchemy.Numeric(10, 2), default=500),
    sqlalchemy.Column("auto_copy_enabled", sqlalchemy.Boolean, default=True),
    sqlalchemy.Column("copy_ratio", sqlalchemy.Numeric(5, 2), default=1.0),
    sqlalchemy.Column("max_lot_size", sqlalchemy.Numeric(8, 2), default=1.0),
    sqlalchemy.Column("created_at", sqlalchemy.DateTime),
    sqlalchemy.Column("updated_at", sqlalchemy.DateTime),
)

mt5_accounts_table = sqlalchemy.Table(
    "mt5_accounts",
    metadata,
    sqlalchemy.Column("id", sqlalchemy.String, primary_key=True),
    sqlalchemy.Column("user_id", sqlalchemy.String, sqlalchemy.ForeignKey("users.id")),
    sqlalchemy.Column("account_number", sqlalchemy.String, nullable=False),
    sqlalchemy.Column("account_name", sqlalchemy.String),
    sqlalchemy.Column("broker_name", sqlalchemy.String, nullable=False),
    sqlalchemy.Column("server", sqlalchemy.String, nullable=False),
    sqlalchemy.Column("is_active", sqlalchemy.Boolean, default=True),
    sqlalchemy.Column("balance", sqlalchemy.Numeric(15, 2), default=0),
    sqlalchemy.Column("equity", sqlalchemy.Numeric(15, 2), default=0),
    sqlalchemy.Column("margin", sqlalchemy.Numeric(15, 2), default=0),
    sqlalchemy.Column("free_margin", sqlalchemy.Numeric(15, 2), default=0),
    sqlalchemy.Column("currency", sqlalchemy.String, default="USD"),
    sqlalchemy.Column("leverage", sqlalchemy.Integer, default=100),
    sqlalchemy.Column("last_sync", sqlalchemy.DateTime),
    sqlalchemy.Column("created_at", sqlalchemy.DateTime),
    sqlalchemy.Column("updated_at", sqlalchemy.DateTime),
)

subscriptions_table = sqlalchemy.Table(
    "subscriptions",
    metadata,
    sqlalchemy.Column("id", sqlalchemy.String, primary_key=True),
    sqlalchemy.Column("follower_id", sqlalchemy.String, sqlalchemy.ForeignKey("followers.follower_id")),
    sqlalchemy.Column("provider_id", sqlalchemy.String, sqlalchemy.ForeignKey("providers.provider_id")),
    sqlalchemy.Column("copy_ratio", sqlalchemy.Numeric(5, 2), default=1.0),
    sqlalchemy.Column("is_active", sqlalchemy.Boolean, default=True),
    sqlalchemy.Column("subscribed_at", sqlalchemy.DateTime),
    sqlalchemy.Column("expires_at", sqlalchemy.DateTime),
    sqlalchemy.Column("created_at", sqlalchemy.DateTime),
    sqlalchemy.Column("updated_at", sqlalchemy.DateTime),
)

signals_table = sqlalchemy.Table(
    "signals",
    metadata,
    sqlalchemy.Column("id", sqlalchemy.String, primary_key=True),
    sqlalchemy.Column("signal_id", sqlalchemy.String, unique=True, nullable=False),
    sqlalchemy.Column("provider_id", sqlalchemy.String, sqlalchemy.ForeignKey("providers.provider_id")),
    sqlalchemy.Column("action", sqlalchemy.Enum("open", "close", "modify", name="signal_action_enum")),
    sqlalchemy.Column("symbol", sqlalchemy.String, nullable=False),
    sqlalchemy.Column("type", sqlalchemy.Enum("buy", "sell", name="signal_type_enum")),
    sqlalchemy.Column("volume", sqlalchemy.Numeric(8, 2)),
    sqlalchemy.Column("open_price", sqlalchemy.Numeric(10, 5)),
    sqlalchemy.Column("stop_loss", sqlalchemy.Numeric(10, 5)),
    sqlalchemy.Column("take_profit", sqlalchemy.Numeric(10, 5)),
    sqlalchemy.Column("comment", sqlalchemy.Text),
    sqlalchemy.Column("original_ticket", sqlalchemy.BigInteger),
    # Partial close fields
    sqlalchemy.Column("partial_close_volume", sqlalchemy.Numeric(8, 2)),
    sqlalchemy.Column("remaining_volume", sqlalchemy.Numeric(8, 2)),
    sqlalchemy.Column("is_partial_close", sqlalchemy.Boolean, default=False),
    sqlalchemy.Column("parent_signal_id", sqlalchemy.String),
    sqlalchemy.Column("status", sqlalchemy.Enum("pending", "distributed", "completed", "cancelled", "error", name="signal_status_enum")),
    sqlalchemy.Column("created_at", sqlalchemy.DateTime),
    sqlalchemy.Column("processed_at", sqlalchemy.DateTime),
)

trades_table = sqlalchemy.Table(
    "trades",
    metadata,
    sqlalchemy.Column("id", sqlalchemy.String, primary_key=True),
    sqlalchemy.Column("signal_id", sqlalchemy.String, sqlalchemy.ForeignKey("signals.signal_id")),
    sqlalchemy.Column("follower_id", sqlalchemy.String, sqlalchemy.ForeignKey("followers.follower_id")),
    sqlalchemy.Column("mt5_ticket", sqlalchemy.BigInteger),
    sqlalchemy.Column("symbol", sqlalchemy.String, nullable=False),
    sqlalchemy.Column("trade_type", sqlalchemy.Enum("buy", "sell", name="trade_type_enum")),
    sqlalchemy.Column("lot_size", sqlalchemy.Numeric(8, 2)),
    sqlalchemy.Column("entry_price", sqlalchemy.Numeric(10, 5)),
    sqlalchemy.Column("exit_price", sqlalchemy.Numeric(10, 5)),
    sqlalchemy.Column("stop_loss", sqlalchemy.Numeric(10, 5)),
    sqlalchemy.Column("take_profit", sqlalchemy.Numeric(10, 5)),
    sqlalchemy.Column("profit_loss", sqlalchemy.Numeric(10, 2)),
    sqlalchemy.Column("commission", sqlalchemy.Numeric(10, 2)),
    sqlalchemy.Column("swap", sqlalchemy.Numeric(10, 2)),
    # Partial close fields
    sqlalchemy.Column("original_volume", sqlalchemy.Numeric(8, 2)),
    sqlalchemy.Column("closed_volume", sqlalchemy.Numeric(8, 2)),
    sqlalchemy.Column("remaining_volume", sqlalchemy.Numeric(8, 2)),
    sqlalchemy.Column("is_partial_close", sqlalchemy.Boolean, default=False),
    sqlalchemy.Column("parent_trade_id", sqlalchemy.String),
    sqlalchemy.Column("status", sqlalchemy.Enum("open", "closed", "cancelled", name="trade_status_enum")),
    sqlalchemy.Column("opened_at", sqlalchemy.DateTime),
    sqlalchemy.Column("closed_at", sqlalchemy.DateTime),
    sqlalchemy.Column("created_at", sqlalchemy.DateTime),
)

performance_metrics_table = sqlalchemy.Table(
    "performance_metrics",
    metadata,
    sqlalchemy.Column("id", sqlalchemy.String, primary_key=True),
    sqlalchemy.Column("provider_id", sqlalchemy.String, sqlalchemy.ForeignKey("providers.provider_id")),
    sqlalchemy.Column("metric_date", sqlalchemy.Date),
    sqlalchemy.Column("total_profit", sqlalchemy.Numeric(15, 2), default=0),
    sqlalchemy.Column("total_loss", sqlalchemy.Numeric(15, 2), default=0),
    sqlalchemy.Column("total_trades", sqlalchemy.Integer, default=0),
    sqlalchemy.Column("winning_trades", sqlalchemy.Integer, default=0),
    sqlalchemy.Column("losing_trades", sqlalchemy.Integer, default=0),
    sqlalchemy.Column("win_rate", sqlalchemy.Numeric(5, 2), default=0),
    sqlalchemy.Column("max_drawdown", sqlalchemy.Numeric(5, 2), default=0),
    sqlalchemy.Column("sharpe_ratio", sqlalchemy.Numeric(8, 4), default=0),
    sqlalchemy.Column("profit_factor", sqlalchemy.Numeric(8, 4), default=0),
    sqlalchemy.Column("created_at", sqlalchemy.DateTime),
    sqlalchemy.Column("updated_at", sqlalchemy.DateTime),
)

execution_results_table = sqlalchemy.Table(
    "execution_results",
    metadata,
    sqlalchemy.Column("id", sqlalchemy.String, primary_key=True),
    sqlalchemy.Column("execution_id", sqlalchemy.String, unique=True, nullable=False),
    sqlalchemy.Column("signal_id", sqlalchemy.String, sqlalchemy.ForeignKey("signals.signal_id")),
    sqlalchemy.Column("follower_id", sqlalchemy.String, sqlalchemy.ForeignKey("followers.follower_id")),
    sqlalchemy.Column("success", sqlalchemy.Boolean, default=False),
    sqlalchemy.Column("ticket", sqlalchemy.BigInteger),
    sqlalchemy.Column("executed_volume", sqlalchemy.Numeric(8, 2)),
    sqlalchemy.Column("executed_price", sqlalchemy.Numeric(10, 5)),
    sqlalchemy.Column("error_message", sqlalchemy.Text),
    sqlalchemy.Column("error_code", sqlalchemy.Integer),
    sqlalchemy.Column("execution_time", sqlalchemy.DateTime),
    sqlalchemy.Column("created_at", sqlalchemy.DateTime),
)

# System tables
api_keys_table = sqlalchemy.Table(
    "api_keys",
    metadata,
    sqlalchemy.Column("id", sqlalchemy.String, primary_key=True),
    sqlalchemy.Column("user_id", sqlalchemy.String, sqlalchemy.ForeignKey("users.id")),
    sqlalchemy.Column("key_name", sqlalchemy.String, nullable=False),
    sqlalchemy.Column("api_key", sqlalchemy.String, unique=True, nullable=False),
    sqlalchemy.Column("permissions", sqlalchemy.JSON),
    sqlalchemy.Column("is_active", sqlalchemy.Boolean, default=True),
    sqlalchemy.Column("expires_at", sqlalchemy.DateTime),
    sqlalchemy.Column("last_used", sqlalchemy.DateTime),
    sqlalchemy.Column("created_at", sqlalchemy.DateTime),
)

system_settings_table = sqlalchemy.Table(
    "system_settings",
    metadata,
    sqlalchemy.Column("id", sqlalchemy.String, primary_key=True),
    sqlalchemy.Column("setting_key", sqlalchemy.String, unique=True, nullable=False),
    sqlalchemy.Column("setting_value", sqlalchemy.JSON),
    sqlalchemy.Column("description", sqlalchemy.Text),
    sqlalchemy.Column("is_public", sqlalchemy.Boolean, default=False),
    sqlalchemy.Column("created_at", sqlalchemy.DateTime),
    sqlalchemy.Column("updated_at", sqlalchemy.DateTime),
)

async def init_db():
    """Initialize database connection"""
    await database.connect()

async def close_db():
    """Close database connection"""
    await database.disconnect()

def create_tables():
    """Create all tables"""
    metadata.create_all(engine)

def drop_tables():
    """Drop all tables"""
    metadata.drop_all(engine)

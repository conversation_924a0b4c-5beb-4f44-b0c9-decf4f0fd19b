"""
Redis client service for caching and real-time data
"""

import redis.asyncio as redis
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

from ..config import settings

logger = logging.getLogger(__name__)

class RedisClient:
    """Redis client for caching and real-time operations"""
    
    def __init__(self):
        self.redis_url = settings.REDIS_URL
        self.redis_password = settings.REDIS_PASSWORD
        self.redis_db = settings.REDIS_DB
        self.client: Optional[redis.Redis] = None
        
        # Key prefixes
        self.SIGNAL_PREFIX = "signal:"
        self.USER_PREFIX = "user:"
        self.MT5_CLIENT_PREFIX = "mt5_client:"
        self.MESSAGE_QUEUE_PREFIX = "msg_queue:"
        self.SUBSCRIPTION_PREFIX = "subscription:"
        self.STATS_PREFIX = "stats:"
        self.RATE_LIMIT_PREFIX = "rate_limit:"
    
    async def connect(self):
        """Connect to <PERSON><PERSON>"""
        try:
            self.client = redis.from_url(
                self.redis_url,
                password=self.redis_password,
                db=self.redis_db,
                decode_responses=True
            )
            
            # Test connection
            await self.client.ping()
            logger.info("Connected to Redis successfully")
            
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            raise
    
    async def disconnect(self):
        """Disconnect from Redis"""
        if self.client:
            await self.client.close()
            logger.info("Disconnected from Redis")
    
    async def ping(self) -> bool:
        """Ping Redis to check connection"""
        try:
            if self.client:
                await self.client.ping()
                return True
        except Exception as e:
            logger.error(f"Redis ping failed: {e}")
        return False
    
    # Signal operations
    async def cache_signal(self, signal_id: str, signal_data: Dict[str, Any], ttl: int = 3600):
        """Cache signal data"""
        key = f"{self.SIGNAL_PREFIX}{signal_id}"
        await self.client.setex(key, ttl, json.dumps(signal_data, default=str))
    
    async def get_signal(self, signal_id: str) -> Optional[Dict[str, Any]]:
        """Get cached signal data"""
        key = f"{self.SIGNAL_PREFIX}{signal_id}"
        data = await self.client.get(key)
        if data:
            return json.loads(data)
        return None
    
    async def update_signal_status(self, signal_id: str, status: str):
        """Update signal status in cache"""
        key = f"{self.SIGNAL_PREFIX}{signal_id}"
        data = await self.client.get(key)
        if data:
            signal_data = json.loads(data)
            signal_data["status"] = status
            signal_data["processed_at"] = datetime.utcnow().isoformat()
            await self.client.setex(key, 3600, json.dumps(signal_data, default=str))
    
    # MT5 Client operations
    async def register_mt5_client(self, client_id: str, user_data: Dict[str, Any], ttl: int = 300):
        """Register MT5 client"""
        key = f"{self.MT5_CLIENT_PREFIX}{client_id}"
        client_info = {
            **user_data,
            "last_seen": datetime.utcnow().isoformat(),
            "registered_at": datetime.utcnow().isoformat()
        }
        await self.client.setex(key, ttl, json.dumps(client_info, default=str))
    
    async def update_client_heartbeat(self, client_id: str):
        """Update client last seen timestamp"""
        key = f"{self.MT5_CLIENT_PREFIX}{client_id}"
        data = await self.client.get(key)
        if data:
            client_info = json.loads(data)
            client_info["last_seen"] = datetime.utcnow().isoformat()
            await self.client.setex(key, 300, json.dumps(client_info, default=str))
    
    async def get_mt5_client(self, client_id: str) -> Optional[Dict[str, Any]]:
        """Get MT5 client info"""
        key = f"{self.MT5_CLIENT_PREFIX}{client_id}"
        data = await self.client.get(key)
        if data:
            return json.loads(data)
        return None
    
    # Message queue operations
    async def queue_message(self, client_id: str, message: Dict[str, Any], ttl: int = 300):
        """Queue message for MT5 client"""
        queue_key = f"{self.MESSAGE_QUEUE_PREFIX}{client_id}"
        message["queued_at"] = datetime.utcnow().isoformat()
        
        # Add to list
        await self.client.lpush(queue_key, json.dumps(message, default=str))
        
        # Set expiry
        await self.client.expire(queue_key, ttl)
        
        # Limit queue size
        await self.client.ltrim(queue_key, 0, 99)  # Keep last 100 messages
    
    async def get_pending_messages(self, client_id: str) -> List[Dict[str, Any]]:
        """Get pending messages for client"""
        queue_key = f"{self.MESSAGE_QUEUE_PREFIX}{client_id}"
        
        # Get all messages and clear queue
        messages = await self.client.lrange(queue_key, 0, -1)
        if messages:
            await self.client.delete(queue_key)
        
        # Parse messages
        parsed_messages = []
        for msg in messages:
            try:
                parsed_messages.append(json.loads(msg))
            except json.JSONDecodeError:
                logger.warning(f"Failed to parse message: {msg}")
        
        return parsed_messages
    
    # Subscription operations
    async def set_client_subscriptions(self, client_id: str, provider_ids: List[str]):
        """Set client subscriptions"""
        key = f"{self.SUBSCRIPTION_PREFIX}{client_id}"
        await self.client.setex(key, 3600, json.dumps(provider_ids))
    
    async def get_client_subscriptions(self, client_id: str) -> List[str]:
        """Get client subscriptions"""
        key = f"{self.SUBSCRIPTION_PREFIX}{client_id}"
        data = await self.client.get(key)
        if data:
            return json.loads(data)
        return []
    
    async def get_provider_followers(self, provider_id: str) -> List[str]:
        """Get followers for a provider"""
        pattern = f"{self.SUBSCRIPTION_PREFIX}*"
        followers = []
        
        async for key in self.client.scan_iter(match=pattern):
            subscriptions = await self.client.get(key)
            if subscriptions:
                provider_list = json.loads(subscriptions)
                if provider_id in provider_list:
                    client_id = key.replace(self.SUBSCRIPTION_PREFIX, "")
                    followers.append(client_id)
        
        return followers
    
    # Statistics operations
    async def increment_daily_signal_count(self, provider_id: str = None):
        """Increment daily signal count"""
        today = datetime.utcnow().strftime("%Y-%m-%d")
        
        # Global count
        global_key = f"{self.STATS_PREFIX}signals:daily:{today}"
        await self.client.incr(global_key)
        await self.client.expire(global_key, 86400 * 7)  # Keep for 7 days
        
        # Provider-specific count
        if provider_id:
            provider_key = f"{self.STATS_PREFIX}signals:daily:{today}:{provider_id}"
            await self.client.incr(provider_key)
            await self.client.expire(provider_key, 86400 * 7)
    
    async def get_daily_signal_count(self, provider_id: str = None) -> int:
        """Get daily signal count"""
        today = datetime.utcnow().strftime("%Y-%m-%d")
        
        if provider_id:
            key = f"{self.STATS_PREFIX}signals:daily:{today}:{provider_id}"
        else:
            key = f"{self.STATS_PREFIX}signals:daily:{today}"
        
        count = await self.client.get(key)
        return int(count) if count else 0
    
    async def get_active_providers_count(self) -> int:
        """Get count of active providers"""
        pattern = f"{self.MT5_CLIENT_PREFIX}provider_*"
        count = 0
        async for key in self.client.scan_iter(match=pattern):
            count += 1
        return count
    
    async def get_active_followers_count(self) -> int:
        """Get count of active followers"""
        pattern = f"{self.MT5_CLIENT_PREFIX}follower_*"
        count = 0
        async for key in self.client.scan_iter(match=pattern):
            count += 1
        return count
    
    # Rate limiting operations
    async def check_rate_limit(self, key: str, limit: int, window: int) -> bool:
        """Check if rate limit is exceeded"""
        current_time = datetime.utcnow()
        window_start = current_time - timedelta(seconds=window)
        
        rate_key = f"{self.RATE_LIMIT_PREFIX}{key}"
        
        # Remove old entries
        await self.client.zremrangebyscore(rate_key, 0, window_start.timestamp())
        
        # Count current requests
        current_count = await self.client.zcard(rate_key)
        
        if current_count >= limit:
            return False
        
        # Add current request
        await self.client.zadd(rate_key, {str(current_time.timestamp()): current_time.timestamp()})
        await self.client.expire(rate_key, window)
        
        return True
    
    # Cache operations
    async def set_cache(self, key: str, value: Any, ttl: int = 3600):
        """Set cache value"""
        await self.client.setex(key, ttl, json.dumps(value, default=str))
    
    async def get_cache(self, key: str) -> Optional[Any]:
        """Get cache value"""
        data = await self.client.get(key)
        if data:
            return json.loads(data)
        return None
    
    async def delete_cache(self, key: str):
        """Delete cache value"""
        await self.client.delete(key)
    
    async def clear_pattern(self, pattern: str):
        """Clear all keys matching pattern"""
        async for key in self.client.scan_iter(match=pattern):
            await self.client.delete(key)
    
    # Health check
    async def health_check(self) -> Dict[str, Any]:
        """Get Redis health status"""
        try:
            info = await self.client.info()
            return {
                "status": "healthy",
                "connected_clients": info.get("connected_clients", 0),
                "used_memory": info.get("used_memory_human", "0B"),
                "uptime": info.get("uptime_in_seconds", 0)
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }

"""
WebSocket Manager
Manages WebSocket connections and real-time communication
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional, Set
import json
from fastapi import WebSocket

logger = logging.getLogger(__name__)

class WebSocketManager:
    """Manages WebSocket connections for real-time communication"""
    
    def __init__(self):
        # Active connections: client_id -> WebSocket
        self.active_connections: Dict[str, WebSocket] = {}
        
        # Client metadata: client_id -> user_data
        self.client_metadata: Dict[str, Dict] = {}
        
        # Provider subscriptions: provider_id -> set of follower_client_ids
        self.provider_subscriptions: Dict[str, Set[str]] = {}
        
        # Connection timestamps
        self.connection_times: Dict[str, datetime] = {}
        
        # Message queues for offline clients
        self.message_queues: Dict[str, List[Dict]] = {}
        
        # Health check
        self.is_running = True
        
        # Start background tasks
        asyncio.create_task(self._heartbeat_task())
        asyncio.create_task(self._cleanup_task())
    
    async def connect(self, client_id: str, websocket: WebSocket, user_data: Dict):
        """Register a new WebSocket connection"""
        try:
            # Store connection
            self.active_connections[client_id] = websocket
            self.client_metadata[client_id] = user_data
            self.connection_times[client_id] = datetime.utcnow()
            
            # Send welcome message
            welcome_message = {
                "type": "welcome",
                "client_id": client_id,
                "timestamp": datetime.utcnow().isoformat(),
                "message": "Connected to Copy Trading System"
            }
            
            await self._send_message(websocket, welcome_message)
            
            # Send any queued messages
            await self._send_queued_messages(client_id)
            
            # Update subscriptions if follower
            if user_data.get("user_type") == "follower":
                await self._update_follower_subscriptions(client_id, user_data)
            
            logger.info(f"WebSocket client connected: {client_id}")
            
        except Exception as e:
            logger.error(f"Connection error for {client_id}: {e}")
            await self.disconnect(client_id)
    
    async def disconnect(self, client_id: str):
        """Remove WebSocket connection"""
        try:
            # Remove from active connections
            if client_id in self.active_connections:
                del self.active_connections[client_id]
            
            # Clean up metadata
            if client_id in self.client_metadata:
                user_data = self.client_metadata[client_id]
                del self.client_metadata[client_id]
                
                # Remove from subscriptions if follower
                if user_data.get("user_type") == "follower":
                    await self._remove_follower_subscriptions(client_id)
            
            # Clean up timestamps
            if client_id in self.connection_times:
                del self.connection_times[client_id]
            
            logger.info(f"WebSocket client disconnected: {client_id}")
            
        except Exception as e:
            logger.error(f"Disconnect error for {client_id}: {e}")
    
    async def send_to_client(self, client_id: str, message: Dict) -> bool:
        """Send message to specific client"""
        try:
            if client_id in self.active_connections:
                websocket = self.active_connections[client_id]
                await self._send_message(websocket, message)
                return True
            else:
                # Queue message for offline client
                await self._queue_message(client_id, message)
                return False
                
        except Exception as e:
            logger.error(f"Send error to {client_id}: {e}")
            # Remove broken connection
            await self.disconnect(client_id)
            return False
    
    async def send_to_followers(self, provider_id: str, message: Dict) -> Dict[str, bool]:
        """Send message to all followers of a provider"""
        results = {}
        
        if provider_id not in self.provider_subscriptions:
            return results
        
        follower_clients = self.provider_subscriptions[provider_id].copy()
        
        for client_id in follower_clients:
            success = await self.send_to_client(client_id, message)
            results[client_id] = success
        
        return results
    
    async def broadcast_to_all(self, message: Dict, user_type: Optional[str] = None) -> int:
        """Broadcast message to all connected clients or specific user type"""
        sent_count = 0
        
        for client_id, websocket in self.active_connections.copy().items():
            try:
                # Filter by user type if specified
                if user_type:
                    client_user_type = self.client_metadata.get(client_id, {}).get("user_type")
                    if client_user_type != user_type:
                        continue
                
                await self._send_message(websocket, message)
                sent_count += 1
                
            except Exception as e:
                logger.error(f"Broadcast error to {client_id}: {e}")
                await self.disconnect(client_id)
        
        return sent_count
    
    async def get_client_count(self) -> int:
        """Get number of active connections"""
        return len(self.active_connections)
    
    async def get_clients_by_type(self, user_type: str) -> List[str]:
        """Get client IDs by user type"""
        clients = []
        
        for client_id, metadata in self.client_metadata.items():
            if metadata.get("user_type") == user_type:
                clients.append(client_id)
        
        return clients
    
    def is_healthy(self) -> bool:
        """Check if WebSocket manager is healthy"""
        return self.is_running
    
    async def _send_message(self, websocket: WebSocket, message: Dict):
        """Send message to WebSocket"""
        try:
            message_str = json.dumps(message, default=str)
            await websocket.send_text(message_str)
            
        except Exception as e:
            logger.error(f"WebSocket send error: {e}")
            raise
    
    async def _queue_message(self, client_id: str, message: Dict):
        """Queue message for offline client"""
        if client_id not in self.message_queues:
            self.message_queues[client_id] = []
        
        # Add timestamp to message
        message["queued_at"] = datetime.utcnow().isoformat()
        
        self.message_queues[client_id].append(message)
        
        # Limit queue size
        max_queue_size = 100
        if len(self.message_queues[client_id]) > max_queue_size:
            self.message_queues[client_id] = self.message_queues[client_id][-max_queue_size:]
        
        logger.debug(f"Message queued for offline client: {client_id}")
    
    async def _send_queued_messages(self, client_id: str):
        """Send queued messages to reconnected client"""
        if client_id not in self.message_queues:
            return
        
        queued_messages = self.message_queues[client_id]
        del self.message_queues[client_id]
        
        if not queued_messages:
            return
        
        # Send queued messages notification
        notification = {
            "type": "queued_messages",
            "count": len(queued_messages),
            "timestamp": datetime.utcnow().isoformat()
        }
        
        await self.send_to_client(client_id, notification)
        
        # Send each queued message
        for message in queued_messages:
            await self.send_to_client(client_id, message)
        
        logger.info(f"Sent {len(queued_messages)} queued messages to {client_id}")
    
    async def _update_follower_subscriptions(self, client_id: str, user_data: Dict):
        """Update provider subscriptions for follower"""
        try:
            # Get follower's subscribed providers from database
            from ..models.database import database
            
            query = """
            SELECT provider_id 
            FROM subscriptions 
            WHERE follower_id = :follower_id AND is_active = true
            """
            
            subscriptions = await database.fetch_all(
                query, 
                {"follower_id": user_data.get("follower_id")}
            )
            
            # Update subscriptions
            for subscription in subscriptions:
                provider_id = subscription["provider_id"]
                
                if provider_id not in self.provider_subscriptions:
                    self.provider_subscriptions[provider_id] = set()
                
                self.provider_subscriptions[provider_id].add(client_id)
            
            logger.debug(f"Updated subscriptions for {client_id}: {len(subscriptions)} providers")
            
        except Exception as e:
            logger.error(f"Subscription update error for {client_id}: {e}")
    
    async def _remove_follower_subscriptions(self, client_id: str):
        """Remove follower from all provider subscriptions"""
        for provider_id, followers in self.provider_subscriptions.items():
            followers.discard(client_id)
            
            # Clean up empty subscription sets
            if not followers:
                del self.provider_subscriptions[provider_id]
    
    async def _heartbeat_task(self):
        """Send periodic heartbeat to all clients"""
        while self.is_running:
            try:
                await asyncio.sleep(30)  # Send heartbeat every 30 seconds
                
                heartbeat_message = {
                    "type": "heartbeat",
                    "timestamp": datetime.utcnow().isoformat(),
                    "active_connections": len(self.active_connections)
                }
                
                # Send to all clients
                for client_id in list(self.active_connections.keys()):
                    try:
                        await self.send_to_client(client_id, heartbeat_message)
                    except Exception as e:
                        logger.warning(f"Heartbeat failed for {client_id}: {e}")
                
            except Exception as e:
                logger.error(f"Heartbeat task error: {e}")
    
    async def _cleanup_task(self):
        """Cleanup old queued messages and stale connections"""
        while self.is_running:
            try:
                await asyncio.sleep(300)  # Run every 5 minutes
                
                current_time = datetime.utcnow()
                
                # Clean up old queued messages (older than 1 hour)
                for client_id, messages in list(self.message_queues.items()):
                    filtered_messages = []
                    
                    for message in messages:
                        if "queued_at" in message:
                            queued_time = datetime.fromisoformat(message["queued_at"])
                            if (current_time - queued_time).total_seconds() < 3600:
                                filtered_messages.append(message)
                    
                    if filtered_messages:
                        self.message_queues[client_id] = filtered_messages
                    else:
                        del self.message_queues[client_id]
                
                # Clean up stale connections (no activity for 10 minutes)
                stale_clients = []
                for client_id, connect_time in self.connection_times.items():
                    if (current_time - connect_time).total_seconds() > 600:
                        if client_id in self.active_connections:
                            stale_clients.append(client_id)
                
                for client_id in stale_clients:
                    logger.warning(f"Removing stale connection: {client_id}")
                    await self.disconnect(client_id)
                
                logger.debug("WebSocket cleanup completed")
                
            except Exception as e:
                logger.error(f"Cleanup task error: {e}")
    
    async def shutdown(self):
        """Shutdown WebSocket manager"""
        self.is_running = False
        
        # Close all connections
        for client_id in list(self.active_connections.keys()):
            await self.disconnect(client_id)
        
        logger.info("WebSocket manager shutdown completed")

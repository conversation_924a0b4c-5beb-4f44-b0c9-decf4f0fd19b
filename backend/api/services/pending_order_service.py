"""
Service for handling pending orders and preventing duplicates
"""

import logging
from typing import Dict, List, Optional, Set
from datetime import datetime, timedelta
from decimal import Decimal
import uuid

from ..models.database import database
from ..services.redis_client import RedisClient

logger = logging.getLogger(__name__)

class PendingOrderService:
    """Service for tracking pending orders and preventing duplicates"""
    
    def __init__(self, redis_client: RedisClient):
        self.redis_client = redis_client
        self.PENDING_ORDER_PREFIX = "pending_order:"
        self.POSITION_MAPPING_PREFIX = "position_mapping:"
    
    async def track_pending_order(self, provider_id: str, order_ticket: int, signal_id: str):
        """Track a pending order to prevent duplicates when it becomes a position"""
        key = f"{self.PENDING_ORDER_PREFIX}{provider_id}:{order_ticket}"
        
        order_data = {
            "signal_id": signal_id,
            "provider_id": provider_id,
            "order_ticket": order_ticket,
            "created_at": datetime.utcnow().isoformat(),
            "status": "pending"
        }
        
        # Store with 24 hour expiry (pending orders usually expire within a day)
        await self.redis_client.set_cache(key, order_data, ttl=86400)
        
        logger.debug(f"Tracking pending order: {provider_id}:{order_ticket}")
    
    async def check_pending_order_exists(self, provider_id: str, order_ticket: int) -> bool:
        """Check if a pending order is already tracked"""
        key = f"{self.PENDING_ORDER_PREFIX}{provider_id}:{order_ticket}"
        order_data = await self.redis_client.get_cache(key)
        return order_data is not None
    
    async def mark_pending_order_as_position(self, provider_id: str, order_ticket: int, position_ticket: int):
        """Mark a pending order as having become a position"""
        pending_key = f"{self.PENDING_ORDER_PREFIX}{provider_id}:{order_ticket}"
        mapping_key = f"{self.POSITION_MAPPING_PREFIX}{provider_id}:{position_ticket}"
        
        # Get pending order data
        order_data = await self.redis_client.get_cache(pending_key)
        
        if order_data:
            # Create position mapping
            position_data = {
                "original_order_ticket": order_ticket,
                "position_ticket": position_ticket,
                "signal_id": order_data.get("signal_id"),
                "provider_id": provider_id,
                "converted_at": datetime.utcnow().isoformat()
            }
            
            # Store position mapping
            await self.redis_client.set_cache(mapping_key, position_data, ttl=86400)
            
            # Update pending order status
            order_data["status"] = "converted_to_position"
            order_data["position_ticket"] = position_ticket
            order_data["converted_at"] = datetime.utcnow().isoformat()
            
            await self.redis_client.set_cache(pending_key, order_data, ttl=86400)
            
            logger.info(f"Marked pending order {order_ticket} as position {position_ticket}")
    
    async def is_position_from_pending_order(self, provider_id: str, position_ticket: int) -> bool:
        """Check if a position came from a tracked pending order"""
        mapping_key = f"{self.POSITION_MAPPING_PREFIX}{provider_id}:{position_ticket}"
        mapping_data = await self.redis_client.get_cache(mapping_key)
        return mapping_data is not None
    
    async def get_original_order_for_position(self, provider_id: str, position_ticket: int) -> Optional[int]:
        """Get the original pending order ticket for a position"""
        mapping_key = f"{self.POSITION_MAPPING_PREFIX}{provider_id}:{position_ticket}"
        mapping_data = await self.redis_client.get_cache(mapping_key)
        
        if mapping_data:
            return mapping_data.get("original_order_ticket")
        
        return None
    
    async def cleanup_expired_pending_orders(self, provider_id: str):
        """Clean up expired pending orders"""
        pattern = f"{self.PENDING_ORDER_PREFIX}{provider_id}:*"
        
        # Get all pending orders for provider
        async for key in self.redis_client.client.scan_iter(match=pattern):
            order_data = await self.redis_client.get_cache(key)
            
            if order_data and order_data.get("status") == "pending":
                created_at = datetime.fromisoformat(order_data["created_at"])
                
                # If older than 24 hours, mark as expired
                if datetime.utcnow() - created_at > timedelta(hours=24):
                    order_data["status"] = "expired"
                    await self.redis_client.set_cache(key, order_data, ttl=3600)  # Keep for 1 hour
                    
                    logger.debug(f"Marked pending order as expired: {key}")
    
    async def get_pending_orders_stats(self, provider_id: str) -> Dict[str, int]:
        """Get statistics about pending orders for a provider"""
        pattern = f"{self.PENDING_ORDER_PREFIX}{provider_id}:*"
        
        stats = {
            "total": 0,
            "pending": 0,
            "converted": 0,
            "expired": 0
        }
        
        async for key in self.redis_client.client.scan_iter(match=pattern):
            order_data = await self.redis_client.get_cache(key)
            
            if order_data:
                stats["total"] += 1
                status = order_data.get("status", "unknown")
                
                if status == "pending":
                    stats["pending"] += 1
                elif status == "converted_to_position":
                    stats["converted"] += 1
                elif status == "expired":
                    stats["expired"] += 1
        
        return stats

class DuplicateSignalPrevention:
    """Service for preventing duplicate signals"""
    
    def __init__(self, redis_client: RedisClient):
        self.redis_client = redis_client
        self.SIGNAL_HASH_PREFIX = "signal_hash:"
        self.PROCESSED_POSITION_PREFIX = "processed_pos:"
    
    def generate_signal_hash(self, provider_id: str, symbol: str, action: str, 
                           ticket: int, timestamp: datetime) -> str:
        """Generate a hash for signal deduplication"""
        # Create hash based on key signal properties
        hash_string = f"{provider_id}:{symbol}:{action}:{ticket}:{timestamp.strftime('%Y%m%d%H%M')}"
        return hash_string
    
    async def is_signal_duplicate(self, signal_hash: str) -> bool:
        """Check if a signal is a duplicate"""
        key = f"{self.SIGNAL_HASH_PREFIX}{signal_hash}"
        exists = await self.redis_client.get_cache(key)
        return exists is not None
    
    async def mark_signal_as_processed(self, signal_hash: str, signal_id: str):
        """Mark a signal as processed to prevent duplicates"""
        key = f"{self.SIGNAL_HASH_PREFIX}{signal_hash}"
        
        signal_data = {
            "signal_id": signal_id,
            "processed_at": datetime.utcnow().isoformat(),
            "hash": signal_hash
        }
        
        # Store for 1 hour (signals older than 1 hour are unlikely to be duplicated)
        await self.redis_client.set_cache(key, signal_data, ttl=3600)
    
    async def is_position_already_processed(self, provider_id: str, position_ticket: int) -> bool:
        """Check if a position has already been processed"""
        key = f"{self.PROCESSED_POSITION_PREFIX}{provider_id}:{position_ticket}"
        exists = await self.redis_client.get_cache(key)
        return exists is not None
    
    async def mark_position_as_processed(self, provider_id: str, position_ticket: int, signal_id: str):
        """Mark a position as processed"""
        key = f"{self.PROCESSED_POSITION_PREFIX}{provider_id}:{position_ticket}"
        
        position_data = {
            "signal_id": signal_id,
            "provider_id": provider_id,
            "position_ticket": position_ticket,
            "processed_at": datetime.utcnow().isoformat()
        }
        
        # Store for 24 hours
        await self.redis_client.set_cache(key, position_data, ttl=86400)

class SignalValidationService:
    """Service for validating signals and preventing issues"""
    
    def __init__(self):
        pass
    
    async def validate_pending_order_signal(self, signal_data: Dict) -> Dict[str, any]:
        """Validate pending order signal"""
        errors = []
        
        # Check required fields
        required_fields = ["provider_id", "symbol", "type", "volume", "open_price"]
        for field in required_fields:
            if not signal_data.get(field):
                errors.append(f"Missing required field: {field}")
        
        # Validate volume
        volume = signal_data.get("volume", 0)
        if volume <= 0:
            errors.append("Volume must be positive")
        
        # Validate price
        open_price = signal_data.get("open_price", 0)
        if open_price <= 0:
            errors.append("Open price must be positive")
        
        # Validate symbol
        symbol = signal_data.get("symbol", "")
        if len(symbol) < 3:
            errors.append("Invalid symbol")
        
        # Check if signal is too old
        if "timestamp" in signal_data:
            try:
                signal_time = datetime.fromisoformat(signal_data["timestamp"])
                age_seconds = (datetime.utcnow() - signal_time).total_seconds()
                
                if age_seconds > 300:  # 5 minutes
                    errors.append("Signal is too old")
            except:
                errors.append("Invalid timestamp format")
        
        return {
            "is_valid": len(errors) == 0,
            "errors": errors
        }
    
    async def validate_position_signal(self, signal_data: Dict) -> Dict[str, any]:
        """Validate position signal"""
        errors = []
        
        # Check required fields
        required_fields = ["provider_id", "symbol", "type", "volume"]
        for field in required_fields:
            if not signal_data.get(field):
                errors.append(f"Missing required field: {field}")
        
        # Validate volume
        volume = signal_data.get("volume", 0)
        if volume <= 0:
            errors.append("Volume must be positive")
        
        # For partial close, validate partial close fields
        if signal_data.get("is_partial_close", False):
            partial_volume = signal_data.get("partial_close_volume", 0)
            remaining_volume = signal_data.get("remaining_volume", 0)
            
            if partial_volume <= 0:
                errors.append("Partial close volume must be positive")
            
            if remaining_volume <= 0:
                errors.append("Remaining volume must be positive")
        
        return {
            "is_valid": len(errors) == 0,
            "errors": errors
        }

# Global instances
pending_order_service = PendingOrderService(RedisClient())
duplicate_prevention = DuplicateSignalPrevention(RedisClient())
signal_validation = SignalValidationService()

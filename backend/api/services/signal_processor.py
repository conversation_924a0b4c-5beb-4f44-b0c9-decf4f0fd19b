"""
Signal Processing Service
Handles signal validation, processing, and distribution
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import json
import uuid

from ..models.database import database
from ..models.schemas import SignalCreate, SignalResponse
from .websocket_manager import WebSocketManager
from .redis_client import RedisClient
from .risk_manager import RiskManager

logger = logging.getLogger(__name__)

class SignalProcessor:
    """Signal processing and distribution service"""
    
    def __init__(self):
        self.websocket_manager = None
        self.redis_client = None
        self.risk_manager = None
        self.running = False
        self.processing_queue = asyncio.Queue()
        
    async def start(self):
        """Start the signal processor"""
        self.websocket_manager = WebSocketManager()
        self.redis_client = RedisClient()
        self.risk_manager = RiskManager()
        
        self.running = True
        
        # Start background tasks
        asyncio.create_task(self._process_signals())
        asyncio.create_task(self._cleanup_old_signals())
        
        logger.info("Signal processor started")
    
    async def stop(self):
        """Stop the signal processor"""
        self.running = False
        logger.info("Signal processor stopped")
    
    async def process_signal(self, signal_data: Dict) -> Dict:
        """Process incoming signal from provider"""
        try:
            # Validate signal data
            if not await self._validate_signal(signal_data):
                raise ValueError("Signal validation failed")
            
            # Create signal record
            signal = await self._create_signal_record(signal_data)
            
            # Add to processing queue
            await self.processing_queue.put(signal)
            
            logger.info(f"Signal queued for processing: {signal['signal_id']}")
            
            return {
                "signal_id": signal["signal_id"],
                "status": "queued",
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Signal processing error: {e}")
            raise
    
    async def _process_signals(self):
        """Background task to process signals from queue"""
        while self.running:
            try:
                # Get signal from queue with timeout
                signal = await asyncio.wait_for(
                    self.processing_queue.get(), 
                    timeout=1.0
                )
                
                await self._distribute_signal(signal)
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"Signal processing error: {e}")
    
    async def _validate_signal(self, signal_data: Dict) -> bool:
        """Validate incoming signal"""
        required_fields = [
            "provider_id", "action", "symbol", "timestamp"
        ]
        
        # Check required fields
        for field in required_fields:
            if field not in signal_data:
                logger.warning(f"Missing required field: {field}")
                return False
        
        # Validate provider exists and is active
        provider = await self._get_provider(signal_data["provider_id"])
        if not provider or not provider["is_active"]:
            logger.warning(f"Invalid or inactive provider: {signal_data['provider_id']}")
            return False
        
        # Validate action type
        valid_actions = ["open", "close", "modify"]
        if signal_data["action"] not in valid_actions:
            logger.warning(f"Invalid action: {signal_data['action']}")
            return False
        
        # Validate symbol
        if not signal_data["symbol"] or len(signal_data["symbol"]) < 3:
            logger.warning(f"Invalid symbol: {signal_data['symbol']}")
            return False
        
        # Additional validation for open signals
        if signal_data["action"] == "open":
            required_open_fields = ["type", "volume", "open_price"]
            for field in required_open_fields:
                if field not in signal_data:
                    logger.warning(f"Missing field for open signal: {field}")
                    return False
            
            # Validate volume
            if signal_data["volume"] <= 0 or signal_data["volume"] > 100:
                logger.warning(f"Invalid volume: {signal_data['volume']}")
                return False
        
        return True
    
    async def _create_signal_record(self, signal_data: Dict) -> Dict:
        """Create signal record in database"""
        signal_id = str(uuid.uuid4())
        
        signal = {
            "signal_id": signal_id,
            "provider_id": signal_data["provider_id"],
            "action": signal_data["action"],
            "symbol": signal_data["symbol"],
            "type": signal_data.get("type"),
            "volume": signal_data.get("volume"),
            "open_price": signal_data.get("open_price"),
            "stop_loss": signal_data.get("stop_loss"),
            "take_profit": signal_data.get("take_profit"),
            "comment": signal_data.get("comment", ""),
            "original_ticket": signal_data.get("ticket"),
            "status": "pending",
            "created_at": datetime.utcnow(),
            "processed_at": None
        }
        
        # Insert into database
        query = """
        INSERT INTO signals (
            signal_id, provider_id, action, symbol, type, volume,
            open_price, stop_loss, take_profit, comment, original_ticket,
            status, created_at
        ) VALUES (
            :signal_id, :provider_id, :action, :symbol, :type, :volume,
            :open_price, :stop_loss, :take_profit, :comment, :original_ticket,
            :status, :created_at
        )
        """
        
        await database.execute(query, signal)
        
        # Cache in Redis
        await self.redis_client.cache_signal(signal_id, signal)
        
        logger.info(f"Signal record created: {signal_id}")
        
        return signal
    
    async def _distribute_signal(self, signal: Dict):
        """Distribute signal to followers"""
        try:
            # Get followers for this provider
            followers = await self._get_provider_followers(signal["provider_id"])
            
            if not followers:
                logger.info(f"No followers for provider: {signal['provider_id']}")
                await self._update_signal_status(signal["signal_id"], "completed")
                return
            
            # Apply risk management filters
            filtered_followers = await self._filter_followers_by_risk(signal, followers)
            
            # Distribute to WebSocket clients
            distribution_results = await self._send_to_websocket_clients(signal, filtered_followers)
            
            # Update signal status
            if any(distribution_results.values()):
                await self._update_signal_status(signal["signal_id"], "distributed")
            else:
                await self._update_signal_status(signal["signal_id"], "failed")
            
            # Log distribution results
            successful_count = sum(1 for success in distribution_results.values() if success)
            logger.info(f"Signal {signal['signal_id']} distributed to {successful_count}/{len(filtered_followers)} followers")
            
        except Exception as e:
            logger.error(f"Signal distribution error: {e}")
            await self._update_signal_status(signal["signal_id"], "error")
    
    async def _get_provider(self, provider_id: str) -> Optional[Dict]:
        """Get provider information"""
        query = """
        SELECT provider_id, user_id, is_active, is_verified
        FROM providers 
        WHERE provider_id = :provider_id
        """
        
        return await database.fetch_one(query, {"provider_id": provider_id})
    
    async def _get_provider_followers(self, provider_id: str) -> List[Dict]:
        """Get active followers for a provider"""
        query = """
        SELECT f.follower_id, f.user_id, f.copy_ratio, f.max_volume,
               f.risk_percent, s.is_active
        FROM followers f
        JOIN subscriptions s ON f.follower_id = s.follower_id
        WHERE s.provider_id = :provider_id 
        AND s.is_active = true
        AND f.auto_copy_enabled = true
        """
        
        return await database.fetch_all(query, {"provider_id": provider_id})
    
    async def _filter_followers_by_risk(self, signal: Dict, followers: List[Dict]) -> List[Dict]:
        """Filter followers based on risk management rules"""
        filtered_followers = []
        
        for follower in followers:
            try:
                # Check if follower can take this trade
                if await self.risk_manager.can_take_trade(follower, signal):
                    filtered_followers.append(follower)
                else:
                    logger.debug(f"Follower {follower['follower_id']} filtered out by risk management")
                    
            except Exception as e:
                logger.error(f"Risk check error for follower {follower['follower_id']}: {e}")
        
        return filtered_followers
    
    async def _send_to_websocket_clients(self, signal: Dict, followers: List[Dict]) -> Dict[str, bool]:
        """Send signal to WebSocket clients"""
        results = {}
        
        for follower in followers:
            client_id = f"follower_{follower['follower_id']}"
            
            try:
                # Prepare signal message
                message = {
                    "type": "signal",
                    "signal_id": signal["signal_id"],
                    "provider_id": signal["provider_id"],
                    "action": signal["action"],
                    "symbol": signal["symbol"],
                    "type": signal.get("type"),
                    "volume": signal.get("volume"),
                    "open_price": signal.get("open_price"),
                    "stop_loss": signal.get("stop_loss"),
                    "take_profit": signal.get("take_profit"),
                    "comment": signal.get("comment"),
                    "original_ticket": signal.get("original_ticket"),
                    "copy_ratio": follower["copy_ratio"],
                    "timestamp": datetime.utcnow().isoformat()
                }
                
                # Send via WebSocket
                success = await self.websocket_manager.send_to_client(client_id, message)
                results[client_id] = success
                
                if success:
                    logger.debug(f"Signal sent to {client_id}")
                else:
                    logger.warning(f"Failed to send signal to {client_id}")
                    
            except Exception as e:
                logger.error(f"Error sending signal to {client_id}: {e}")
                results[client_id] = False
        
        return results
    
    async def _update_signal_status(self, signal_id: str, status: str):
        """Update signal status in database"""
        query = """
        UPDATE signals 
        SET status = :status, processed_at = :processed_at
        WHERE signal_id = :signal_id
        """
        
        await database.execute(query, {
            "signal_id": signal_id,
            "status": status,
            "processed_at": datetime.utcnow()
        })
        
        # Update cache
        await self.redis_client.update_signal_status(signal_id, status)
    
    async def _cleanup_old_signals(self):
        """Background task to cleanup old signals"""
        while self.running:
            try:
                # Sleep for 1 hour
                await asyncio.sleep(3600)
                
                # Delete signals older than 7 days
                cutoff_date = datetime.utcnow() - timedelta(days=7)
                
                query = """
                DELETE FROM signals 
                WHERE created_at < :cutoff_date
                """
                
                result = await database.execute(query, {"cutoff_date": cutoff_date})
                
                if result:
                    logger.info(f"Cleaned up old signals: {result} records deleted")
                    
            except Exception as e:
                logger.error(f"Signal cleanup error: {e}")
    
    async def get_signal_status(self, signal_id: str) -> Optional[Dict]:
        """Get signal status"""
        # Try cache first
        cached_signal = await self.redis_client.get_signal(signal_id)
        if cached_signal:
            return cached_signal
        
        # Fallback to database
        query = """
        SELECT signal_id, status, created_at, processed_at
        FROM signals
        WHERE signal_id = :signal_id
        """
        
        return await database.fetch_one(query, {"signal_id": signal_id})

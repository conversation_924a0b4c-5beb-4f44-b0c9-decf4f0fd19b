"""
Service for handling partial position closes
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from decimal import Decimal
import uuid

from ..models.database import database
from ..models.schemas import SignalCreate, SignalResponse, TradeUpdate
from ..services.redis_client import RedisClient

logger = logging.getLogger(__name__)

class PartialCloseService:
    """Service for handling partial position closes"""
    
    def __init__(self, redis_client: RedisClient):
        self.redis_client = redis_client
    
    async def process_partial_close_signal(self, signal_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process partial close signal from provider"""
        try:
            # Validate partial close signal
            if not self._validate_partial_close_signal(signal_data):
                return {"success": False, "error": "Invalid partial close signal"}
            
            # Create signal record
            signal_id = await self._create_partial_close_signal(signal_data)
            
            if not signal_id:
                return {"success": False, "error": "Failed to create signal"}
            
            # Get affected followers
            followers = await self._get_provider_followers(signal_data["provider_id"])
            
            # Distribute signal to followers
            distribution_results = await self._distribute_partial_close_signal(
                signal_id, signal_data, followers
            )
            
            # Update signal status
            await self._update_signal_status(signal_id, "distributed")
            
            return {
                "success": True,
                "signal_id": signal_id,
                "distributed_to": len(followers),
                "distribution_results": distribution_results
            }
            
        except Exception as e:
            logger.error(f"Error processing partial close signal: {e}")
            return {"success": False, "error": str(e)}
    
    def _validate_partial_close_signal(self, signal_data: Dict[str, Any]) -> bool:
        """Validate partial close signal data"""
        required_fields = [
            "provider_id", "action", "symbol", "original_ticket",
            "partial_close_volume", "remaining_volume"
        ]
        
        for field in required_fields:
            if field not in signal_data:
                logger.warning(f"Missing required field: {field}")
                return False
        
        # Validate action
        if signal_data["action"] != "close":
            logger.warning("Invalid action for partial close")
            return False
        
        # Validate is_partial_close flag
        if not signal_data.get("is_partial_close", False):
            logger.warning("is_partial_close flag not set")
            return False
        
        # Validate volumes
        partial_volume = Decimal(str(signal_data["partial_close_volume"]))
        remaining_volume = Decimal(str(signal_data["remaining_volume"]))
        
        if partial_volume <= 0:
            logger.warning("Invalid partial close volume")
            return False
        
        if remaining_volume <= 0:
            logger.warning("Invalid remaining volume")
            return False
        
        return True
    
    async def _create_partial_close_signal(self, signal_data: Dict[str, Any]) -> Optional[str]:
        """Create partial close signal in database"""
        try:
            signal_id = signal_data.get("signal_id") or f"partial_{uuid.uuid4().hex[:8]}"
            
            query = """
            INSERT INTO signals (
                id, signal_id, provider_id, action, symbol, type,
                volume, open_price, stop_loss, take_profit, comment,
                original_ticket, partial_close_volume, remaining_volume,
                is_partial_close, parent_signal_id, status, created_at
            ) VALUES (
                :id, :signal_id, :provider_id, :action, :symbol, :type,
                :volume, :open_price, :stop_loss, :take_profit, :comment,
                :original_ticket, :partial_close_volume, :remaining_volume,
                :is_partial_close, :parent_signal_id, :status, :created_at
            )
            """
            
            values = {
                "id": str(uuid.uuid4()),
                "signal_id": signal_id,
                "provider_id": signal_data["provider_id"],
                "action": signal_data["action"],
                "symbol": signal_data["symbol"],
                "type": signal_data.get("type"),
                "volume": signal_data.get("volume"),
                "open_price": signal_data.get("open_price"),
                "stop_loss": signal_data.get("stop_loss"),
                "take_profit": signal_data.get("take_profit"),
                "comment": signal_data.get("comment", "Partial close"),
                "original_ticket": signal_data["original_ticket"],
                "partial_close_volume": signal_data["partial_close_volume"],
                "remaining_volume": signal_data["remaining_volume"],
                "is_partial_close": True,
                "parent_signal_id": signal_data.get("parent_signal_id"),
                "status": "pending",
                "created_at": datetime.utcnow()
            }
            
            await database.execute(query, values)
            
            # Cache signal for quick access
            await self.redis_client.cache_signal(signal_id, signal_data)
            
            logger.info(f"Created partial close signal: {signal_id}")
            return signal_id
            
        except Exception as e:
            logger.error(f"Error creating partial close signal: {e}")
            return None
    
    async def _get_provider_followers(self, provider_id: str) -> List[Dict[str, Any]]:
        """Get active followers for a provider"""
        query = """
        SELECT f.follower_id, f.balance, f.copy_ratio, f.max_lot_size,
               s.copy_ratio as subscription_copy_ratio, s.is_active
        FROM subscriptions s
        JOIN followers f ON s.follower_id = f.follower_id
        WHERE s.provider_id = :provider_id 
        AND s.is_active = true
        AND f.auto_copy_enabled = true
        """
        
        followers = await database.fetch_all(query, {"provider_id": provider_id})
        return [dict(follower) for follower in followers]
    
    async def _distribute_partial_close_signal(
        self, 
        signal_id: str, 
        signal_data: Dict[str, Any], 
        followers: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Distribute partial close signal to followers"""
        results = []
        
        for follower in followers:
            try:
                # Calculate follower-specific volumes
                follower_signal = self._calculate_follower_partial_close(
                    signal_data, follower
                )
                
                # Queue signal for follower
                await self.redis_client.queue_message(
                    f"follower_{follower['follower_id']}", 
                    follower_signal
                )
                
                # Create execution record
                execution_id = await self._create_execution_record(
                    signal_id, follower["follower_id"], follower_signal
                )
                
                results.append({
                    "follower_id": follower["follower_id"],
                    "execution_id": execution_id,
                    "status": "queued",
                    "partial_close_volume": follower_signal["partial_close_volume"]
                })
                
                logger.debug(f"Queued partial close for follower: {follower['follower_id']}")
                
            except Exception as e:
                logger.error(f"Error distributing to follower {follower['follower_id']}: {e}")
                results.append({
                    "follower_id": follower["follower_id"],
                    "status": "error",
                    "error": str(e)
                })
        
        return results
    
    def _calculate_follower_partial_close(
        self, 
        signal_data: Dict[str, Any], 
        follower: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Calculate partial close volumes for specific follower"""
        # Get copy ratio (subscription-specific or follower default)
        copy_ratio = Decimal(str(
            follower.get("subscription_copy_ratio") or 
            follower.get("copy_ratio", 1.0)
        ))
        
        # Calculate follower volumes
        original_partial_volume = Decimal(str(signal_data["partial_close_volume"]))
        original_remaining_volume = Decimal(str(signal_data["remaining_volume"]))
        
        follower_partial_volume = original_partial_volume * copy_ratio
        follower_remaining_volume = original_remaining_volume * copy_ratio
        
        # Create follower-specific signal
        follower_signal = signal_data.copy()
        follower_signal.update({
            "follower_id": follower["follower_id"],
            "partial_close_volume": float(follower_partial_volume),
            "remaining_volume": float(follower_remaining_volume),
            "copy_ratio": float(copy_ratio),
            "timestamp": datetime.utcnow().isoformat()
        })
        
        return follower_signal
    
    async def _create_execution_record(
        self, 
        signal_id: str, 
        follower_id: str, 
        signal_data: Dict[str, Any]
    ) -> str:
        """Create execution record for tracking"""
        execution_id = f"exec_{uuid.uuid4().hex[:8]}"
        
        query = """
        INSERT INTO execution_results (
            id, execution_id, signal_id, follower_id, success,
            executed_volume, execution_time, created_at
        ) VALUES (
            :id, :execution_id, :signal_id, :follower_id, :success,
            :executed_volume, :execution_time, :created_at
        )
        """
        
        values = {
            "id": str(uuid.uuid4()),
            "execution_id": execution_id,
            "signal_id": signal_id,
            "follower_id": follower_id,
            "success": False,  # Will be updated when executed
            "executed_volume": signal_data["partial_close_volume"],
            "execution_time": datetime.utcnow(),
            "created_at": datetime.utcnow()
        }
        
        await database.execute(query, values)
        return execution_id
    
    async def _update_signal_status(self, signal_id: str, status: str):
        """Update signal status"""
        query = """
        UPDATE signals 
        SET status = :status, processed_at = :processed_at
        WHERE signal_id = :signal_id
        """
        
        await database.execute(query, {
            "signal_id": signal_id,
            "status": status,
            "processed_at": datetime.utcnow()
        })
        
        # Update cached signal
        await self.redis_client.update_signal_status(signal_id, status)
    
    async def handle_partial_close_execution_result(
        self, 
        execution_id: str, 
        result_data: Dict[str, Any]
    ) -> bool:
        """Handle execution result from follower"""
        try:
            # Update execution record
            query = """
            UPDATE execution_results
            SET success = :success, ticket = :ticket, 
                executed_volume = :executed_volume, executed_price = :executed_price,
                error_message = :error_message, error_code = :error_code
            WHERE execution_id = :execution_id
            """
            
            await database.execute(query, {
                "execution_id": execution_id,
                "success": result_data.get("success", False),
                "ticket": result_data.get("ticket"),
                "executed_volume": result_data.get("executed_volume"),
                "executed_price": result_data.get("executed_price"),
                "error_message": result_data.get("error_message"),
                "error_code": result_data.get("error_code")
            })
            
            # If successful, update trade record
            if result_data.get("success") and result_data.get("ticket"):
                await self._update_trade_for_partial_close(
                    result_data["ticket"], 
                    result_data.get("executed_volume", 0)
                )
            
            logger.info(f"Updated execution result: {execution_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error handling execution result: {e}")
            return False
    
    async def _update_trade_for_partial_close(self, ticket: int, closed_volume: float):
        """Update trade record for partial close"""
        try:
            # Get current trade
            query = """
            SELECT id, lot_size, original_volume, closed_volume, remaining_volume
            FROM trades WHERE mt5_ticket = :ticket AND status = 'open'
            """
            
            trade = await database.fetch_one(query, {"ticket": ticket})
            
            if not trade:
                logger.warning(f"Trade not found for ticket: {ticket}")
                return
            
            # Calculate new volumes
            current_volume = Decimal(str(trade["lot_size"]))
            original_volume = Decimal(str(trade["original_volume"] or current_volume))
            previous_closed = Decimal(str(trade["closed_volume"] or 0))
            
            new_closed_volume = previous_closed + Decimal(str(closed_volume))
            new_remaining_volume = original_volume - new_closed_volume
            
            # Update trade record
            update_query = """
            UPDATE trades
            SET original_volume = :original_volume,
                closed_volume = :closed_volume,
                remaining_volume = :remaining_volume,
                lot_size = :lot_size,
                is_partial_close = true
            WHERE mt5_ticket = :ticket
            """
            
            await database.execute(update_query, {
                "ticket": ticket,
                "original_volume": float(original_volume),
                "closed_volume": float(new_closed_volume),
                "remaining_volume": float(new_remaining_volume),
                "lot_size": float(new_remaining_volume)  # Update current lot size
            })
            
            logger.info(f"Updated trade for partial close: {ticket}")
            
        except Exception as e:
            logger.error(f"Error updating trade for partial close: {e}")

# Global instance
partial_close_service = PartialCloseService(RedisClient())

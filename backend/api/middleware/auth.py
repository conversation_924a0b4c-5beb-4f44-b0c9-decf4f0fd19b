"""
Authentication and Authorization middleware
"""

import jwt
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from fastapi import HTT<PERSON>Exception, status
from fastapi.security import HTTPAuthorizationCredentials
from passlib.context import CryptContext
import uuid

from ..config import settings
from ..models.database import database

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class AuthManager:
    """Authentication and authorization manager"""
    
    def __init__(self):
        self.secret_key = settings.SECRET_KEY
        self.algorithm = settings.ALGORITHM
        self.access_token_expire_minutes = settings.ACCESS_TOKEN_EXPIRE_MINUTES
        self.refresh_token_expire_days = settings.REFRESH_TOKEN_EXPIRE_DAYS
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify password against hash"""
        return pwd_context.verify(plain_password, hashed_password)
    
    def get_password_hash(self, password: str) -> str:
        """Hash password"""
        return pwd_context.hash(password)
    
    def create_access_token(self, data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
        """Create JWT access token"""
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        
        to_encode.update({"exp": expire, "type": "access"})
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    def create_refresh_token(self, data: Dict[str, Any]) -> str:
        """Create JWT refresh token"""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(days=self.refresh_token_expire_days)
        to_encode.update({"exp": expire, "type": "refresh"})
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Verify and decode JWT token"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            return payload
        except jwt.ExpiredSignatureError:
            return None
        except jwt.JWTError:
            return None
    
    async def authenticate_user(self, email: str, password: str) -> Optional[Dict[str, Any]]:
        """Authenticate user with email and password"""
        query = """
        SELECT u.id, u.email, u.password_hash, u.user_type, u.is_active, u.is_verified,
               p.provider_id, f.follower_id
        FROM users u
        LEFT JOIN providers p ON u.id = p.user_id
        LEFT JOIN followers f ON u.id = f.user_id
        WHERE u.email = :email
        """
        
        user = await database.fetch_one(query, {"email": email})
        
        if not user:
            return None
        
        if not self.verify_password(password, user["password_hash"]):
            return None
        
        if not user["is_active"]:
            return None
        
        return {
            "user_id": user["id"],
            "email": user["email"],
            "user_type": user["user_type"],
            "is_verified": user["is_verified"],
            "provider_id": user["provider_id"],
            "follower_id": user["follower_id"]
        }
    
    async def get_user_by_id(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get user by ID"""
        query = """
        SELECT u.id, u.email, u.user_type, u.is_active, u.is_verified,
               p.provider_id, f.follower_id
        FROM users u
        LEFT JOIN providers p ON u.id = p.user_id
        LEFT JOIN followers f ON u.id = f.user_id
        WHERE u.id = :user_id AND u.is_active = true
        """
        
        user = await database.fetch_one(query, {"user_id": user_id})
        
        if not user:
            return None
        
        return {
            "user_id": user["id"],
            "email": user["email"],
            "user_type": user["user_type"],
            "is_verified": user["is_verified"],
            "provider_id": user["provider_id"],
            "follower_id": user["follower_id"]
        }
    
    async def create_api_key(self, user_id: str, key_name: str, permissions: Dict[str, Any] = None) -> str:
        """Create API key for user"""
        api_key = f"ct_{uuid.uuid4().hex}"
        
        query = """
        INSERT INTO api_keys (user_id, key_name, api_key, permissions)
        VALUES (:user_id, :key_name, :api_key, :permissions)
        """
        
        await database.execute(query, {
            "user_id": user_id,
            "key_name": key_name,
            "api_key": api_key,
            "permissions": permissions or {}
        })
        
        return api_key
    
    async def verify_api_key(self, api_key: str) -> Optional[Dict[str, Any]]:
        """Verify API key and return user data"""
        query = """
        SELECT ak.user_id, ak.permissions, u.email, u.user_type, u.is_active,
               p.provider_id, f.follower_id
        FROM api_keys ak
        JOIN users u ON ak.user_id = u.id
        LEFT JOIN providers p ON u.id = p.user_id
        LEFT JOIN followers f ON u.id = f.user_id
        WHERE ak.api_key = :api_key 
        AND ak.is_active = true 
        AND (ak.expires_at IS NULL OR ak.expires_at > NOW())
        AND u.is_active = true
        """
        
        result = await database.fetch_one(query, {"api_key": api_key})
        
        if not result:
            return None
        
        # Update last used timestamp
        update_query = "UPDATE api_keys SET last_used = NOW() WHERE api_key = :api_key"
        await database.execute(update_query, {"api_key": api_key})
        
        return {
            "user_id": result["user_id"],
            "email": result["email"],
            "user_type": result["user_type"],
            "provider_id": result["provider_id"],
            "follower_id": result["follower_id"],
            "permissions": result["permissions"]
        }

# Global auth manager instance
auth_manager = AuthManager()

# Dependency functions
async def verify_token(token: str) -> Optional[Dict[str, Any]]:
    """Verify JWT token or API key"""
    # Try JWT token first
    if token.startswith("eyJ"):  # JWT tokens start with eyJ
        payload = auth_manager.verify_token(token)
        if payload and payload.get("type") == "access":
            user_data = await auth_manager.get_user_by_id(payload.get("user_id"))
            return user_data
    
    # Try API key
    elif token.startswith("ct_"):  # API keys start with ct_
        user_data = await auth_manager.verify_api_key(token)
        return user_data
    
    return None

async def get_current_user(credentials: HTTPAuthorizationCredentials) -> Dict[str, Any]:
    """Get current authenticated user"""
    user_data = await verify_token(credentials.credentials)
    
    if not user_data:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return user_data

async def require_provider(user_data: Dict[str, Any]) -> Dict[str, Any]:
    """Require user to be a provider"""
    if user_data.get("user_type") not in ["provider", "both"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Provider access required"
        )
    
    if not user_data.get("provider_id"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Provider profile not found"
        )
    
    return user_data

async def require_follower(user_data: Dict[str, Any]) -> Dict[str, Any]:
    """Require user to be a follower"""
    if user_data.get("user_type") not in ["follower", "both"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Follower access required"
        )
    
    if not user_data.get("follower_id"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Follower profile not found"
        )
    
    return user_data

async def require_admin(user_data: Dict[str, Any]) -> Dict[str, Any]:
    """Require user to be an admin"""
    # Check if user has admin permissions
    permissions = user_data.get("permissions", {})
    if not permissions.get("admin", False):
        # Check if user email is admin
        if user_data.get("email") != "<EMAIL>":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Admin access required"
            )
    
    return user_data

async def require_verified(user_data: Dict[str, Any]) -> Dict[str, Any]:
    """Require user to be verified"""
    if not user_data.get("is_verified"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Account verification required"
        )
    
    return user_data

# Copy Trading System Backend Dependencies

# Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Database
databases[postgresql]==0.8.0
sqlalchemy==2.0.23
asyncpg==0.29.0
alembic==1.13.0

# Redis
redis==5.0.1
aioredis==2.0.1

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
cryptography==41.0.8

# HTTP Client
httpx==0.25.2
aiohttp==3.9.1

# WebSocket
websockets==12.0

# Validation & Serialization
email-validator==2.1.0
python-dateutil==2.8.2

# Monitoring & Logging
prometheus-client==0.19.0
structlog==23.2.0

# Development & Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2
factory-boy==3.3.0

# Utilities
python-dotenv==1.0.0
click==8.1.7
rich==13.7.0
typer==0.9.0

# Data Processing
pandas==2.1.4
numpy==1.26.2

# Task Queue (optional)
celery==5.3.4
kombu==5.3.4

# File Handling
aiofiles==23.2.1
pillow==10.1.0

# Timezone
pytz==2023.3

# Development Tools
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
pre-commit==3.6.0

# Documentation
mkdocs==1.5.3
mkdocs-material==9.4.8

# Production
gunicorn==21.2.0
supervisor==4.2.5

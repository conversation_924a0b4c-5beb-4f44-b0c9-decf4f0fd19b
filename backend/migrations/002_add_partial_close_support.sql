-- Migration: Add Partial Close Support
-- Description: Add fields to support partial position closing

-- Add partial close fields to signals table
ALTER TABLE signals 
ADD COLUMN partial_close_volume DECIMAL(8,2) DEFAULT NULL,
ADD COLUMN remaining_volume DECIMAL(8,2) DEFAULT NULL,
ADD COLUMN is_partial_close BOOLEAN DEFAULT FALSE,
ADD COLUMN parent_signal_id VARCHAR(100) DEFAULT NULL;

-- Add partial close fields to trades table
ALTER TABLE trades
ADD COLUMN original_volume DECIMAL(8,2) DEFAULT NULL,
ADD COLUMN closed_volume DECIMAL(8,2) DEFAULT NULL,
ADD COLUMN remaining_volume DECIMAL(8,2) DEFAULT NULL,
ADD COLUMN is_partial_close BOOLEAN DEFAULT FALSE,
ADD COLUMN parent_trade_id UUID DEFAULT NULL;

-- Add indexes for performance
CREATE INDEX idx_signals_is_partial_close ON signals(is_partial_close);
CREATE INDEX idx_signals_parent_signal_id ON signals(parent_signal_id);
CREATE INDEX idx_trades_is_partial_close ON trades(is_partial_close);
CREATE INDEX idx_trades_parent_trade_id ON trades(parent_trade_id);

-- Add foreign key constraint for parent_trade_id
ALTER TABLE trades 
ADD CONSTRAINT fk_trades_parent_trade_id 
FOREIGN KEY (parent_trade_id) REFERENCES trades(id);

-- Create view for partial close statistics
CREATE OR REPLACE VIEW partial_close_stats AS
SELECT 
    provider_id,
    DATE(created_at) as trade_date,
    COUNT(*) as total_partial_closes,
    SUM(partial_close_volume) as total_partial_volume,
    AVG(partial_close_volume) as avg_partial_volume,
    COUNT(DISTINCT original_ticket) as unique_positions_affected
FROM signals 
WHERE is_partial_close = true 
AND action = 'close'
GROUP BY provider_id, DATE(created_at);

-- Create function to calculate position remaining volume
CREATE OR REPLACE FUNCTION calculate_position_remaining_volume(
    p_original_ticket BIGINT,
    p_provider_id VARCHAR(50)
) RETURNS DECIMAL(8,2) AS $$
DECLARE
    total_closed DECIMAL(8,2) := 0;
    original_volume DECIMAL(8,2) := 0;
BEGIN
    -- Get original volume from first open signal
    SELECT volume INTO original_volume
    FROM signals 
    WHERE original_ticket = p_original_ticket 
    AND provider_id = p_provider_id 
    AND action = 'open'
    ORDER BY created_at ASC
    LIMIT 1;
    
    -- Calculate total closed volume
    SELECT COALESCE(SUM(partial_close_volume), 0) INTO total_closed
    FROM signals 
    WHERE original_ticket = p_original_ticket 
    AND provider_id = p_provider_id 
    AND action = 'close'
    AND is_partial_close = true;
    
    -- Return remaining volume
    RETURN COALESCE(original_volume - total_closed, 0);
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update remaining volume automatically
CREATE OR REPLACE FUNCTION update_remaining_volume_trigger()
RETURNS TRIGGER AS $$
BEGIN
    -- Only for partial close signals
    IF NEW.is_partial_close = true AND NEW.action = 'close' THEN
        -- Calculate and update remaining volume
        NEW.remaining_volume := calculate_position_remaining_volume(
            NEW.original_ticket, 
            NEW.provider_id
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER signals_update_remaining_volume
    BEFORE INSERT OR UPDATE ON signals
    FOR EACH ROW
    EXECUTE FUNCTION update_remaining_volume_trigger();

-- Create stored procedure for partial close validation
CREATE OR REPLACE FUNCTION validate_partial_close(
    p_provider_id VARCHAR(50),
    p_original_ticket BIGINT,
    p_partial_volume DECIMAL(8,2)
) RETURNS BOOLEAN AS $$
DECLARE
    current_remaining DECIMAL(8,2);
    min_volume DECIMAL(8,2) := 0.01; -- Minimum volume allowed
BEGIN
    -- Calculate current remaining volume
    current_remaining := calculate_position_remaining_volume(p_original_ticket, p_provider_id);
    
    -- Validate partial close volume
    IF p_partial_volume <= 0 THEN
        RETURN FALSE;
    END IF;
    
    IF p_partial_volume >= current_remaining THEN
        RETURN FALSE;
    END IF;
    
    -- Ensure remaining volume after close is above minimum
    IF (current_remaining - p_partial_volume) < min_volume THEN
        RETURN FALSE;
    END IF;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Insert system settings for partial close
INSERT INTO system_settings (setting_key, setting_value, description, is_public) VALUES
('enable_partial_close', 'true', 'Enable partial position closing', true),
('min_partial_close_volume', '0.01', 'Minimum volume for partial close', true),
('max_partial_close_per_position', '10', 'Maximum partial closes per position', true),
('partial_close_min_remaining', '0.01', 'Minimum remaining volume after partial close', true)
ON CONFLICT (setting_key) DO NOTHING;

-- Create audit table for partial close operations
CREATE TABLE partial_close_audit (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    signal_id VARCHAR(100) NOT NULL,
    provider_id VARCHAR(50) NOT NULL,
    original_ticket BIGINT NOT NULL,
    partial_volume DECIMAL(8,2) NOT NULL,
    remaining_volume DECIMAL(8,2) NOT NULL,
    followers_affected INTEGER DEFAULT 0,
    success_rate DECIMAL(5,2) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for audit table
CREATE INDEX idx_partial_close_audit_provider ON partial_close_audit(provider_id);
CREATE INDEX idx_partial_close_audit_ticket ON partial_close_audit(original_ticket);
CREATE INDEX idx_partial_close_audit_created_at ON partial_close_audit(created_at);

-- Update performance metrics table to include partial close metrics
ALTER TABLE performance_metrics 
ADD COLUMN partial_closes_count INTEGER DEFAULT 0,
ADD COLUMN partial_close_volume DECIMAL(15,2) DEFAULT 0,
ADD COLUMN avg_partial_close_size DECIMAL(8,2) DEFAULT 0;

COMMIT;

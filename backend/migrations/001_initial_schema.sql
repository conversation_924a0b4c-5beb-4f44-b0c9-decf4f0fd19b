-- Copy Trading System Database Schema
-- Initial migration script

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create enums
CREATE TYPE user_type_enum AS ENUM ('provider', 'follower', 'both');
CREATE TYPE signal_action_enum AS ENUM ('open', 'close', 'modify');
CREATE TYPE signal_type_enum AS ENUM ('buy', 'sell');
CREATE TYPE signal_status_enum AS ENUM ('pending', 'distributed', 'completed', 'cancelled', 'error');
CREATE TYPE trade_type_enum AS ENUM ('buy', 'sell');
CREATE TYPE trade_status_enum AS ENUM ('open', 'closed', 'cancelled');

-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VA<PERSON>HA<PERSON>(100),
    last_name <PERSON><PERSON><PERSON><PERSON>(100),
    user_type user_type_enum NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Providers table
CREATE TABLE providers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    provider_id VARCHAR(50) UNIQUE NOT NULL,
    display_name VARCHAR(255) NOT NULL,
    description TEXT,
    subscription_fee DECIMAL(10,2) DEFAULT 0,
    max_followers INTEGER DEFAULT 100,
    is_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    min_balance_required DECIMAL(10,2) DEFAULT 0,
    risk_level INTEGER DEFAULT 3 CHECK (risk_level >= 1 AND risk_level <= 5),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Followers table
CREATE TABLE followers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    follower_id VARCHAR(50) UNIQUE NOT NULL,
    balance DECIMAL(15,2) DEFAULT 0,
    max_risk_per_trade DECIMAL(5,2) DEFAULT 2.0,
    max_daily_loss DECIMAL(10,2) DEFAULT 500,
    auto_copy_enabled BOOLEAN DEFAULT TRUE,
    copy_ratio DECIMAL(5,2) DEFAULT 1.0,
    max_lot_size DECIMAL(8,2) DEFAULT 1.0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- MT5 Accounts table
CREATE TABLE mt5_accounts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    account_number VARCHAR(50) NOT NULL,
    account_name VARCHAR(255),
    broker_name VARCHAR(255) NOT NULL,
    server VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    balance DECIMAL(15,2) DEFAULT 0,
    equity DECIMAL(15,2) DEFAULT 0,
    margin DECIMAL(15,2) DEFAULT 0,
    free_margin DECIMAL(15,2) DEFAULT 0,
    currency VARCHAR(10) DEFAULT 'USD',
    leverage INTEGER DEFAULT 100,
    last_sync TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Subscriptions table
CREATE TABLE subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    follower_id VARCHAR(50) NOT NULL REFERENCES followers(follower_id) ON DELETE CASCADE,
    provider_id VARCHAR(50) NOT NULL REFERENCES providers(provider_id) ON DELETE CASCADE,
    copy_ratio DECIMAL(5,2) DEFAULT 1.0,
    is_active BOOLEAN DEFAULT TRUE,
    subscribed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(follower_id, provider_id)
);

-- Signals table
CREATE TABLE signals (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    signal_id VARCHAR(100) UNIQUE NOT NULL,
    provider_id VARCHAR(50) NOT NULL REFERENCES providers(provider_id) ON DELETE CASCADE,
    action signal_action_enum NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    type signal_type_enum,
    volume DECIMAL(8,2),
    open_price DECIMAL(10,5),
    stop_loss DECIMAL(10,5),
    take_profit DECIMAL(10,5),
    comment TEXT,
    original_ticket BIGINT,
    -- Partial close fields
    partial_close_volume DECIMAL(8,2) DEFAULT NULL,
    remaining_volume DECIMAL(8,2) DEFAULT NULL,
    is_partial_close BOOLEAN DEFAULT FALSE,
    parent_signal_id VARCHAR(100) DEFAULT NULL,
    status signal_status_enum DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE
);

-- Trades table
CREATE TABLE trades (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    signal_id VARCHAR(100) NOT NULL REFERENCES signals(signal_id) ON DELETE CASCADE,
    follower_id VARCHAR(50) NOT NULL REFERENCES followers(follower_id) ON DELETE CASCADE,
    mt5_ticket BIGINT,
    symbol VARCHAR(20) NOT NULL,
    trade_type trade_type_enum NOT NULL,
    lot_size DECIMAL(8,2) NOT NULL,
    entry_price DECIMAL(10,5),
    exit_price DECIMAL(10,5),
    stop_loss DECIMAL(10,5),
    take_profit DECIMAL(10,5),
    profit_loss DECIMAL(10,2),
    commission DECIMAL(10,2),
    swap DECIMAL(10,2),
    -- Partial close fields
    original_volume DECIMAL(8,2) DEFAULT NULL,
    closed_volume DECIMAL(8,2) DEFAULT NULL,
    remaining_volume DECIMAL(8,2) DEFAULT NULL,
    is_partial_close BOOLEAN DEFAULT FALSE,
    parent_trade_id UUID DEFAULT NULL,
    status trade_status_enum DEFAULT 'open',
    opened_at TIMESTAMP WITH TIME ZONE,
    closed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Performance Metrics table
CREATE TABLE performance_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    provider_id VARCHAR(50) NOT NULL REFERENCES providers(provider_id) ON DELETE CASCADE,
    metric_date DATE NOT NULL,
    total_profit DECIMAL(15,2) DEFAULT 0,
    total_loss DECIMAL(15,2) DEFAULT 0,
    total_trades INTEGER DEFAULT 0,
    winning_trades INTEGER DEFAULT 0,
    losing_trades INTEGER DEFAULT 0,
    win_rate DECIMAL(5,2) DEFAULT 0,
    max_drawdown DECIMAL(5,2) DEFAULT 0,
    sharpe_ratio DECIMAL(8,4) DEFAULT 0,
    profit_factor DECIMAL(8,4) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(provider_id, metric_date)
);

-- Execution Results table
CREATE TABLE execution_results (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    execution_id VARCHAR(100) UNIQUE NOT NULL,
    signal_id VARCHAR(100) NOT NULL REFERENCES signals(signal_id) ON DELETE CASCADE,
    follower_id VARCHAR(50) NOT NULL REFERENCES followers(follower_id) ON DELETE CASCADE,
    success BOOLEAN DEFAULT FALSE,
    ticket BIGINT,
    executed_volume DECIMAL(8,2),
    executed_price DECIMAL(10,5),
    error_message TEXT,
    error_code INTEGER,
    execution_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- API Keys table
CREATE TABLE api_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    key_name VARCHAR(255) NOT NULL,
    api_key VARCHAR(255) UNIQUE NOT NULL,
    permissions JSONB,
    is_active BOOLEAN DEFAULT TRUE,
    expires_at TIMESTAMP WITH TIME ZONE,
    last_used TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- System Settings table
CREATE TABLE system_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    setting_key VARCHAR(255) UNIQUE NOT NULL,
    setting_value JSONB,
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_user_type ON users(user_type);
CREATE INDEX idx_providers_provider_id ON providers(provider_id);
CREATE INDEX idx_providers_is_active ON providers(is_active);
CREATE INDEX idx_followers_follower_id ON followers(follower_id);
CREATE INDEX idx_subscriptions_follower_provider ON subscriptions(follower_id, provider_id);
CREATE INDEX idx_subscriptions_is_active ON subscriptions(is_active);
CREATE INDEX idx_signals_provider_id ON signals(provider_id);
CREATE INDEX idx_signals_status ON signals(status);
CREATE INDEX idx_signals_created_at ON signals(created_at);
CREATE INDEX idx_trades_signal_id ON trades(signal_id);
CREATE INDEX idx_trades_follower_id ON trades(follower_id);
CREATE INDEX idx_trades_status ON trades(status);
CREATE INDEX idx_performance_provider_date ON performance_metrics(provider_id, metric_date);
CREATE INDEX idx_execution_results_signal_id ON execution_results(signal_id);
CREATE INDEX idx_api_keys_user_id ON api_keys(user_id);
CREATE INDEX idx_api_keys_api_key ON api_keys(api_key);

-- Create triggers for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_providers_updated_at BEFORE UPDATE ON providers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_followers_updated_at BEFORE UPDATE ON followers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_mt5_accounts_updated_at BEFORE UPDATE ON mt5_accounts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_subscriptions_updated_at BEFORE UPDATE ON subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_performance_metrics_updated_at BEFORE UPDATE ON performance_metrics
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_settings_updated_at BEFORE UPDATE ON system_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default system settings
INSERT INTO system_settings (setting_key, setting_value, description, is_public) VALUES
('max_signals_per_day', '100', 'Maximum signals per provider per day', true),
('max_followers_per_provider', '1000', 'Maximum followers per provider', true),
('min_subscription_fee', '0', 'Minimum subscription fee', true),
('max_subscription_fee', '1000', 'Maximum subscription fee', true),
('signal_expiry_minutes', '60', 'Signal expiry time in minutes', false),
('maintenance_mode', 'false', 'System maintenance mode', true);

-- Create admin user (password: admin123)
INSERT INTO users (email, password_hash, first_name, last_name, user_type, is_verified) VALUES
('<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uO8G', 'Admin', 'User', 'both', true);

COMMIT;

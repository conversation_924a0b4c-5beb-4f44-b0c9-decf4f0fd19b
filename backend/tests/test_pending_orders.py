"""
Test cases for pending orders and duplicate prevention
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock

from api.services.pending_order_service import (
    PendingOrderService, 
    DuplicateSignalPrevention,
    SignalValidationService
)
from api.services.redis_client import RedisClient

class TestPendingOrderService:
    """Test pending order tracking service"""
    
    @pytest.fixture
    def mock_redis_client(self):
        mock_redis = AsyncMock(spec=RedisClient)
        mock_redis.set_cache = AsyncMock()
        mock_redis.get_cache = AsyncMock()
        return mock_redis
    
    @pytest.fixture
    def pending_order_service(self, mock_redis_client):
        return PendingOrderService(mock_redis_client)
    
    @pytest.mark.asyncio
    async def test_track_pending_order(self, pending_order_service, mock_redis_client):
        """Test tracking a pending order"""
        provider_id = "provider_123"
        order_ticket = 12345
        signal_id = "signal_abc"
        
        await pending_order_service.track_pending_order(provider_id, order_ticket, signal_id)
        
        # Verify Redis was called with correct parameters
        mock_redis_client.set_cache.assert_called_once()
        call_args = mock_redis_client.set_cache.call_args
        
        assert f"pending_order:{provider_id}:{order_ticket}" in call_args[0][0]
        assert call_args[1]["ttl"] == 86400  # 24 hours
    
    @pytest.mark.asyncio
    async def test_check_pending_order_exists(self, pending_order_service, mock_redis_client):
        """Test checking if pending order exists"""
        provider_id = "provider_123"
        order_ticket = 12345
        
        # Test when order exists
        mock_redis_client.get_cache.return_value = {"signal_id": "test"}
        exists = await pending_order_service.check_pending_order_exists(provider_id, order_ticket)
        assert exists is True
        
        # Test when order doesn't exist
        mock_redis_client.get_cache.return_value = None
        exists = await pending_order_service.check_pending_order_exists(provider_id, order_ticket)
        assert exists is False
    
    @pytest.mark.asyncio
    async def test_mark_pending_order_as_position(self, pending_order_service, mock_redis_client):
        """Test marking pending order as converted to position"""
        provider_id = "provider_123"
        order_ticket = 12345
        position_ticket = 67890
        
        # Mock existing pending order
        mock_redis_client.get_cache.return_value = {
            "signal_id": "signal_abc",
            "provider_id": provider_id,
            "order_ticket": order_ticket,
            "status": "pending"
        }
        
        await pending_order_service.mark_pending_order_as_position(
            provider_id, order_ticket, position_ticket
        )
        
        # Should call set_cache twice (for mapping and updated order)
        assert mock_redis_client.set_cache.call_count == 2
    
    @pytest.mark.asyncio
    async def test_is_position_from_pending_order(self, pending_order_service, mock_redis_client):
        """Test checking if position came from pending order"""
        provider_id = "provider_123"
        position_ticket = 67890
        
        # Test when mapping exists
        mock_redis_client.get_cache.return_value = {"original_order_ticket": 12345}
        result = await pending_order_service.is_position_from_pending_order(provider_id, position_ticket)
        assert result is True
        
        # Test when mapping doesn't exist
        mock_redis_client.get_cache.return_value = None
        result = await pending_order_service.is_position_from_pending_order(provider_id, position_ticket)
        assert result is False

class TestDuplicateSignalPrevention:
    """Test duplicate signal prevention service"""
    
    @pytest.fixture
    def mock_redis_client(self):
        mock_redis = AsyncMock(spec=RedisClient)
        mock_redis.set_cache = AsyncMock()
        mock_redis.get_cache = AsyncMock()
        return mock_redis
    
    @pytest.fixture
    def duplicate_prevention(self, mock_redis_client):
        return DuplicateSignalPrevention(mock_redis_client)
    
    def test_generate_signal_hash(self, duplicate_prevention):
        """Test signal hash generation"""
        provider_id = "provider_123"
        symbol = "EURUSD"
        action = "open"
        ticket = 12345
        timestamp = datetime(2023, 1, 1, 12, 0, 0)
        
        hash1 = duplicate_prevention.generate_signal_hash(
            provider_id, symbol, action, ticket, timestamp
        )
        
        # Same parameters should generate same hash
        hash2 = duplicate_prevention.generate_signal_hash(
            provider_id, symbol, action, ticket, timestamp
        )
        
        assert hash1 == hash2
        assert provider_id in hash1
        assert symbol in hash1
        assert action in hash1
        assert str(ticket) in hash1
    
    @pytest.mark.asyncio
    async def test_is_signal_duplicate(self, duplicate_prevention, mock_redis_client):
        """Test duplicate signal detection"""
        signal_hash = "test_hash_123"
        
        # Test when signal is duplicate
        mock_redis_client.get_cache.return_value = {"signal_id": "existing"}
        is_duplicate = await duplicate_prevention.is_signal_duplicate(signal_hash)
        assert is_duplicate is True
        
        # Test when signal is not duplicate
        mock_redis_client.get_cache.return_value = None
        is_duplicate = await duplicate_prevention.is_signal_duplicate(signal_hash)
        assert is_duplicate is False
    
    @pytest.mark.asyncio
    async def test_mark_signal_as_processed(self, duplicate_prevention, mock_redis_client):
        """Test marking signal as processed"""
        signal_hash = "test_hash_123"
        signal_id = "signal_abc"
        
        await duplicate_prevention.mark_signal_as_processed(signal_hash, signal_id)
        
        # Verify Redis was called
        mock_redis_client.set_cache.assert_called_once()
        call_args = mock_redis_client.set_cache.call_args
        
        assert signal_hash in call_args[0][0]
        assert call_args[1]["ttl"] == 3600  # 1 hour

class TestSignalValidationService:
    """Test signal validation service"""
    
    @pytest.fixture
    def validation_service(self):
        return SignalValidationService()
    
    @pytest.mark.asyncio
    async def test_validate_pending_order_signal_valid(self, validation_service):
        """Test validation of valid pending order signal"""
        signal_data = {
            "provider_id": "provider_123",
            "symbol": "EURUSD",
            "type": "buy",
            "volume": 0.1,
            "open_price": 1.1234,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        result = await validation_service.validate_pending_order_signal(signal_data)
        
        assert result["is_valid"] is True
        assert len(result["errors"]) == 0
    
    @pytest.mark.asyncio
    async def test_validate_pending_order_signal_invalid(self, validation_service):
        """Test validation of invalid pending order signal"""
        signal_data = {
            "provider_id": "",  # Invalid
            "symbol": "EU",     # Too short
            "type": "buy",
            "volume": -0.1,     # Negative
            "open_price": 0,    # Zero
        }
        
        result = await validation_service.validate_pending_order_signal(signal_data)
        
        assert result["is_valid"] is False
        assert len(result["errors"]) > 0
        assert any("provider_id" in error for error in result["errors"])
        assert any("symbol" in error for error in result["errors"])
        assert any("Volume" in error for error in result["errors"])
        assert any("price" in error for error in result["errors"])
    
    @pytest.mark.asyncio
    async def test_validate_position_signal_partial_close(self, validation_service):
        """Test validation of partial close signal"""
        # Valid partial close signal
        valid_signal = {
            "provider_id": "provider_123",
            "symbol": "EURUSD",
            "type": "buy",
            "volume": 0.1,
            "is_partial_close": True,
            "partial_close_volume": 0.05,
            "remaining_volume": 0.05
        }
        
        result = await validation_service.validate_position_signal(valid_signal)
        assert result["is_valid"] is True
        
        # Invalid partial close signal
        invalid_signal = valid_signal.copy()
        invalid_signal["partial_close_volume"] = 0  # Invalid
        
        result = await validation_service.validate_position_signal(invalid_signal)
        assert result["is_valid"] is False
        assert any("Partial close volume" in error for error in result["errors"])
    
    @pytest.mark.asyncio
    async def test_validate_old_signal(self, validation_service):
        """Test validation of old signal"""
        old_timestamp = (datetime.utcnow() - timedelta(minutes=10)).isoformat()
        
        signal_data = {
            "provider_id": "provider_123",
            "symbol": "EURUSD",
            "type": "buy",
            "volume": 0.1,
            "open_price": 1.1234,
            "timestamp": old_timestamp
        }
        
        result = await validation_service.validate_pending_order_signal(signal_data)
        
        assert result["is_valid"] is False
        assert any("too old" in error for error in result["errors"])

class TestIntegration:
    """Integration tests for pending orders and duplicate prevention"""
    
    @pytest.mark.asyncio
    async def test_pending_order_to_position_workflow(self):
        """Test complete workflow from pending order to position"""
        mock_redis = AsyncMock(spec=RedisClient)
        mock_redis.set_cache = AsyncMock()
        mock_redis.get_cache = AsyncMock()
        
        pending_service = PendingOrderService(mock_redis)
        duplicate_service = DuplicateSignalPrevention(mock_redis)
        
        provider_id = "provider_123"
        order_ticket = 12345
        position_ticket = 67890
        signal_id = "signal_abc"
        
        # Step 1: Track pending order
        await pending_service.track_pending_order(provider_id, order_ticket, signal_id)
        
        # Step 2: Mark as converted to position
        mock_redis.get_cache.return_value = {
            "signal_id": signal_id,
            "provider_id": provider_id,
            "order_ticket": order_ticket,
            "status": "pending"
        }
        
        await pending_service.mark_pending_order_as_position(
            provider_id, order_ticket, position_ticket
        )
        
        # Step 3: Check if position came from pending order
        mock_redis.get_cache.return_value = {"original_order_ticket": order_ticket}
        
        is_from_pending = await pending_service.is_position_from_pending_order(
            provider_id, position_ticket
        )
        
        assert is_from_pending is True
        
        # Verify all Redis calls were made
        assert mock_redis.set_cache.call_count >= 2
        assert mock_redis.get_cache.call_count >= 2

# Run tests with: pytest backend/tests/test_pending_orders.py -v

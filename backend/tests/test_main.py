"""
Test cases for the main FastAPI application
"""

import pytest
import asyncio
from httpx import AsyncClient
from fastapi.testclient import Test<PERSON>lient
import json
from datetime import datetime

from main import app
from api.models.database import database
from api.config import settings

# Test client
client = TestClient(app)

class TestHealthEndpoints:
    """Test health check endpoints"""
    
    def test_basic_health_check(self):
        """Test basic health endpoint"""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert data["version"] == "2.0.0"
    
    def test_detailed_health_check(self):
        """Test detailed health endpoint"""
        response = client.get("/health/detailed")
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        assert "components" in data
        assert "timestamp" in data

class TestAuthentication:
    """Test authentication endpoints"""
    
    @pytest.fixture
    def test_user_data(self):
        return {
            "email": "<EMAIL>",
            "password": "testpassword123",
            "first_name": "Test",
            "last_name": "User",
            "user_type": "provider"
        }
    
    def test_user_registration(self, test_user_data):
        """Test user registration"""
        response = client.post("/api/v1/auth/register", json=test_user_data)
        assert response.status_code == 200
        data = response.json()
        assert data["email"] == test_user_data["email"]
        assert data["user_type"] == test_user_data["user_type"]
        assert "id" in data
    
    def test_user_login(self, test_user_data):
        """Test user login"""
        # First register user
        client.post("/api/v1/auth/register", json=test_user_data)
        
        # Then login
        login_data = {
            "email": test_user_data["email"],
            "password": test_user_data["password"]
        }
        response = client.post("/api/v1/auth/login", json=login_data)
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"
        assert "user" in data
    
    def test_invalid_login(self):
        """Test login with invalid credentials"""
        login_data = {
            "email": "<EMAIL>",
            "password": "wrongpassword"
        }
        response = client.post("/api/v1/auth/login", json=login_data)
        assert response.status_code == 401

class TestSignalsAPI:
    """Test signals API endpoints"""
    
    @pytest.fixture
    def auth_headers(self, test_user_data):
        """Get authentication headers"""
        # Register and login user
        client.post("/api/v1/auth/register", json=test_user_data)
        login_response = client.post("/api/v1/auth/login", json={
            "email": test_user_data["email"],
            "password": test_user_data["password"]
        })
        token = login_response.json()["access_token"]
        return {"Authorization": f"Bearer {token}"}
    
    @pytest.fixture
    def sample_signal(self):
        return {
            "provider_id": "provider_test123",
            "action": "open",
            "symbol": "EURUSD",
            "type": "buy",
            "volume": 0.1,
            "open_price": 1.1234,
            "stop_loss": 1.1200,
            "take_profit": 1.1300,
            "comment": "Test signal"
        }
    
    def test_create_signal(self, auth_headers, sample_signal):
        """Test signal creation"""
        response = client.post(
            "/api/v1/signals/",
            json=sample_signal,
            headers=auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "signal_id" in data
    
    def test_get_signals(self, auth_headers):
        """Test getting signals list"""
        response = client.get("/api/v1/signals/", headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    def test_get_signal_by_id(self, auth_headers, sample_signal):
        """Test getting specific signal"""
        # Create signal first
        create_response = client.post(
            "/api/v1/signals/",
            json=sample_signal,
            headers=auth_headers
        )
        signal_id = create_response.json()["signal_id"]
        
        # Get signal
        response = client.get(f"/api/v1/signals/{signal_id}", headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert data["signal_id"] == signal_id

class TestProvidersAPI:
    """Test providers API endpoints"""
    
    def test_list_providers(self):
        """Test listing providers"""
        response = client.get("/api/v1/providers/")
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    def test_get_provider_stats(self, auth_headers):
        """Test getting provider statistics"""
        provider_id = "provider_test123"
        response = client.get(
            f"/api/v1/providers/{provider_id}/stats",
            headers=auth_headers
        )
        # Should return 403 if provider doesn't exist or no access
        assert response.status_code in [200, 403, 404]

class TestFollowersAPI:
    """Test followers API endpoints"""
    
    def test_list_followers(self, auth_headers):
        """Test listing followers"""
        response = client.get("/api/v1/followers/", headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

class TestWebSocketEndpoints:
    """Test WebSocket endpoints"""
    
    @pytest.mark.asyncio
    async def test_websocket_connection(self):
        """Test WebSocket connection"""
        # This would require a more complex setup with actual WebSocket testing
        # For now, just test that the endpoint exists
        pass

class TestRateLimiting:
    """Test rate limiting functionality"""
    
    def test_api_rate_limiting(self):
        """Test API rate limiting"""
        # Make multiple requests quickly
        responses = []
        for i in range(100):
            response = client.get("/health")
            responses.append(response.status_code)
        
        # Should have some successful requests
        assert 200 in responses

class TestErrorHandling:
    """Test error handling"""
    
    def test_404_error(self):
        """Test 404 error handling"""
        response = client.get("/api/v1/nonexistent")
        assert response.status_code == 404
    
    def test_invalid_json(self):
        """Test invalid JSON handling"""
        response = client.post(
            "/api/v1/auth/login",
            data="invalid json",
            headers={"Content-Type": "application/json"}
        )
        assert response.status_code == 422

class TestDataValidation:
    """Test data validation"""
    
    def test_invalid_email_registration(self):
        """Test registration with invalid email"""
        invalid_data = {
            "email": "invalid-email",
            "password": "testpassword123",
            "user_type": "provider"
        }
        response = client.post("/api/v1/auth/register", json=invalid_data)
        assert response.status_code == 422
    
    def test_weak_password_registration(self):
        """Test registration with weak password"""
        invalid_data = {
            "email": "<EMAIL>",
            "password": "123",
            "user_type": "provider"
        }
        response = client.post("/api/v1/auth/register", json=invalid_data)
        assert response.status_code == 422

class TestPerformance:
    """Test performance characteristics"""
    
    def test_response_time(self):
        """Test API response time"""
        import time
        start_time = time.time()
        response = client.get("/health")
        end_time = time.time()
        
        assert response.status_code == 200
        assert (end_time - start_time) < 1.0  # Should respond within 1 second

# Fixtures for database setup/teardown
@pytest.fixture(scope="session", autouse=True)
async def setup_test_database():
    """Setup test database"""
    # Connect to test database
    await database.connect()
    
    # Create tables if needed
    # This would typically use Alembic migrations
    
    yield
    
    # Cleanup
    await database.disconnect()

@pytest.fixture(autouse=True)
async def cleanup_database():
    """Cleanup database after each test"""
    yield
    
    # Clean up test data
    # This would typically truncate tables or use transactions

# Test configuration
@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

# Run tests with: pytest backend/tests/ -v

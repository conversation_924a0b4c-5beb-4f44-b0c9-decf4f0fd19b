//+------------------------------------------------------------------+
//| ProviderEA.mq5                                                   |
//| Professional Copy Trading Provider EA                            |
//| Captures and sends trading signals to backend server            |
//+------------------------------------------------------------------+
#property copyright "Copy Trading System"
#property version   "2.00"
#property description "Provider EA for Copy Trading System"

#include "includes/HttpClient.mqh"
#include "includes/SignalManager.mqh"
#include "includes/ConfigManager.mqh"
#include "includes/Logger.mqh"

//--- Input parameters
input group "=== API Configuration ==="
input string    API_BASE_URL = "https://api.copytrading.com";
input string    API_KEY = "";
input string    PROVIDER_ID = "";
input bool      ENABLE_SSL = true;

input group "=== Signal Settings ==="
input bool      AUTO_SEND_SIGNALS = true;
input int       MIN_PROFIT_POINTS = 10;
input double    MIN_VOLUME = 0.01;
input bool      SEND_PENDING_ORDERS = false;
input bool      SEND_MODIFICATIONS = true;

input group "=== Risk Management ==="
input double    MAX_DAILY_SIGNALS = 50;
input double    MAX_SIGNAL_VOLUME = 10.0;
input bool      VALIDATE_SIGNALS = true;

input group "=== System Settings ==="
input int       HEARTBEAT_INTERVAL = 30;  // seconds
input int       RETRY_ATTEMPTS = 3;
input int       RETRY_DELAY = 1000;       // milliseconds
input bool      ENABLE_LOGGING = true;

//--- Global variables
CHttpClient*    httpClient;
CSignalManager* signalManager;
CConfigManager* configManager;
CLogger*        logger;

datetime        lastHeartbeat = 0;
int             dailySignalCount = 0;
datetime        lastResetDate = 0;
bool            isInitialized = false;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit() {
    Print("=== Provider EA Initialization ===");
    
    // Validate input parameters
    if(!ValidateInputs()) {
        return INIT_PARAMETERS_INCORRECT;
    }
    
    // Initialize components
    if(!InitializeComponents()) {
        return INIT_FAILED;
    }
    
    // Perform initial setup
    if(!PerformInitialSetup()) {
        return INIT_FAILED;
    }
    
    // Set timer for periodic tasks
    EventSetTimer(1);
    
    isInitialized = true;
    logger.Info("Provider EA initialized successfully");
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    logger.Info(StringFormat("Provider EA stopping. Reason: %d", reason));
    
    // Send offline status
    if(isInitialized && httpClient != NULL) {
        SendProviderStatus("offline");
    }
    
    // Cleanup components
    CleanupComponents();
    
    EventKillTimer();
    Print("Provider EA stopped");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick() {
    if(!isInitialized) return;
    
    // Update signal manager with current market data
    signalManager.OnTick();
}

//+------------------------------------------------------------------+
//| Timer function                                                   |
//+------------------------------------------------------------------+
void OnTimer() {
    if(!isInitialized) return;
    
    // Reset daily counter
    ResetDailyCounterIfNeeded();
    
    // Send heartbeat
    SendHeartbeatIfNeeded();
    
    // Process pending signals
    signalManager.ProcessPendingSignals();
    
    // Check connection status
    CheckConnectionStatus();
}

//+------------------------------------------------------------------+
//| Trade event handler                                              |
//+------------------------------------------------------------------+
void OnTrade() {
    if(!isInitialized || !AUTO_SEND_SIGNALS) return;
    
    logger.Debug("Trade event detected");
    
    // Check daily limit
    if(dailySignalCount >= MAX_DAILY_SIGNALS) {
        logger.Warning("Daily signal limit reached");
        return;
    }
    
    // Process new trades
    ProcessTradeEvents();
}

//+------------------------------------------------------------------+
//| Validate input parameters                                        |
//+------------------------------------------------------------------+
bool ValidateInputs() {
    if(StringLen(API_BASE_URL) == 0) {
        Alert("API_BASE_URL is required");
        return false;
    }
    
    if(StringLen(API_KEY) == 0) {
        Alert("API_KEY is required");
        return false;
    }
    
    if(StringLen(PROVIDER_ID) == 0) {
        Alert("PROVIDER_ID is required");
        return false;
    }
    
    if(MAX_DAILY_SIGNALS <= 0) {
        Alert("MAX_DAILY_SIGNALS must be positive");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Initialize components                                            |
//+------------------------------------------------------------------+
bool InitializeComponents() {
    // Initialize logger first
    logger = new CLogger(ENABLE_LOGGING);
    if(logger == NULL) {
        Print("Failed to initialize logger");
        return false;
    }
    
    // Initialize config manager
    configManager = new CConfigManager();
    if(configManager == NULL) {
        logger.Error("Failed to initialize config manager");
        return false;
    }
    
    // Initialize HTTP client
    httpClient = new CHttpClient(API_BASE_URL, API_KEY, ENABLE_SSL);
    if(httpClient == NULL) {
        logger.Error("Failed to initialize HTTP client");
        return false;
    }
    
    // Initialize signal manager
    signalManager = new CSignalManager(httpClient, logger);
    if(signalManager == NULL) {
        logger.Error("Failed to initialize signal manager");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Perform initial setup                                           |
//+------------------------------------------------------------------+
bool PerformInitialSetup() {
    // Load configuration
    if(!configManager.LoadConfig()) {
        logger.Warning("Failed to load config, using defaults");
    }
    
    // Test API connection
    if(!TestApiConnection()) {
        logger.Error("Failed to connect to API");
        return false;
    }
    
    // Register provider
    if(!RegisterProvider()) {
        logger.Error("Failed to register provider");
        return false;
    }
    
    // Send online status
    SendProviderStatus("online");
    
    return true;
}

//+------------------------------------------------------------------+
//| Test API connection                                              |
//+------------------------------------------------------------------+
bool TestApiConnection() {
    logger.Info("Testing API connection...");
    
    string endpoint = "/api/v1/health";
    string response;
    int statusCode;
    
    if(httpClient.Get(endpoint, response, statusCode)) {
        if(statusCode == 200) {
            logger.Info("API connection successful");
            return true;
        }
    }
    
    logger.Error(StringFormat("API connection failed. Status: %d", statusCode));
    return false;
}

//+------------------------------------------------------------------+
//| Register provider with backend                                  |
//+------------------------------------------------------------------+
bool RegisterProvider() {
    logger.Info("Registering provider...");
    
    // Prepare registration data
    string jsonData = StringFormat(
        "{"
        "\"provider_id\":\"%s\","
        "\"account_number\":%d,"
        "\"account_name\":\"%s\","
        "\"broker\":\"%s\","
        "\"server\":\"%s\","
        "\"balance\":%.2f,"
        "\"equity\":%.2f,"
        "\"currency\":\"%s\","
        "\"leverage\":%d,"
        "\"timestamp\":%d"
        "}",
        PROVIDER_ID,
        AccountInfoInteger(ACCOUNT_LOGIN),
        AccountInfoString(ACCOUNT_NAME),
        AccountInfoString(ACCOUNT_COMPANY),
        AccountInfoString(ACCOUNT_SERVER),
        AccountInfoDouble(ACCOUNT_BALANCE),
        AccountInfoDouble(ACCOUNT_EQUITY),
        AccountInfoString(ACCOUNT_CURRENCY),
        AccountInfoInteger(ACCOUNT_LEVERAGE),
        TimeCurrent()
    );
    
    string endpoint = "/api/v1/providers/register";
    string response;
    int statusCode;
    
    if(httpClient.Post(endpoint, jsonData, response, statusCode)) {
        if(statusCode == 200 || statusCode == 201) {
            logger.Info("Provider registered successfully");
            return true;
        }
    }
    
    logger.Error(StringFormat("Provider registration failed. Status: %d, Response: %s", 
                             statusCode, response));
    return false;
}

//+------------------------------------------------------------------+
//| Process trade events                                             |
//+------------------------------------------------------------------+
void ProcessTradeEvents() {
    // Check for new positions
    CheckNewPositions();
    
    // Check for closed positions
    CheckClosedPositions();
    
    // Check for modified positions
    if(SEND_MODIFICATIONS) {
        CheckModifiedPositions();
    }
    
    // Check for pending orders
    if(SEND_PENDING_ORDERS) {
        CheckPendingOrders();
    }
}

//+------------------------------------------------------------------+
//| Check for new positions                                          |
//+------------------------------------------------------------------+
void CheckNewPositions() {
    static datetime lastCheck = 0;
    datetime currentTime = TimeCurrent();
    
    for(int i = PositionsTotal() - 1; i >= 0; i--) {
        if(PositionSelectByIndex(i)) {
            datetime openTime = (datetime)PositionGetInteger(POSITION_TIME);
            
            if(openTime > lastCheck) {
                ProcessNewPosition();
            }
        }
    }
    
    lastCheck = currentTime;
}

//+------------------------------------------------------------------+
//| Process new position                                             |
//+------------------------------------------------------------------+
void ProcessNewPosition() {
    ulong ticket = PositionGetInteger(POSITION_TICKET);
    string symbol = PositionGetString(POSITION_SYMBOL);
    double volume = PositionGetDouble(POSITION_VOLUME);
    
    // Validate signal
    if(VALIDATE_SIGNALS && !ValidateSignal(symbol, volume)) {
        logger.Warning(StringFormat("Signal validation failed for position %d", ticket));
        return;
    }
    
    // Create signal data
    SignalData signal;
    if(CreateSignalFromPosition(signal)) {
        // Send signal
        if(signalManager.SendSignal(signal)) {
            dailySignalCount++;
            logger.Info(StringFormat("Signal sent for position %d", ticket));
        }
    }
}

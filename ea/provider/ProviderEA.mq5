//+------------------------------------------------------------------+
//| ProviderEA.mq5                                                   |
//| Professional Copy Trading Provider EA                            |
//| Captures and sends trading signals to backend server            |
//+------------------------------------------------------------------+
#property copyright "Copy Trading System"
#property version   "2.00"
#property description "Provider EA for Copy Trading System"

#include "../includes/SignalData.mqh"
#include "includes/HttpClient.mqh"
#include "includes/SignalManager.mqh"
#include "includes/ConfigManager.mqh"
#include "includes/Logger.mqh"

//--- Input parameters
input group "=== API Configuration ==="
input string    API_BASE_URL = "https://api.copytrading.com";
input string    API_KEY = "";
input string    PROVIDER_ID = "";
input bool      ENABLE_SSL = true;

input group "=== Signal Settings ==="
input bool      AUTO_SEND_SIGNALS = true;
input int       MIN_PROFIT_POINTS = 10;
input double    MIN_VOLUME = 0.01;
input bool      SEND_PENDING_ORDERS = false;
input bool      SEND_MODIFICATIONS = true;

input group "=== Risk Management ==="
input double    MAX_DAILY_SIGNALS = 50;
input double    MAX_SIGNAL_VOLUME = 10.0;
input bool      VALIDATE_SIGNALS = true;

input group "=== System Settings ==="
input int       HEARTBEAT_INTERVAL = 30;  // seconds
input int       RETRY_ATTEMPTS = 3;
input int       RETRY_DELAY = 1000;       // milliseconds
input bool      ENABLE_LOGGING = true;

//--- Global variables
CHttpClient*    httpClient;
CSignalManager* signalManager;
CConfigManager* configManager;
CLogger*        logger;

datetime        lastHeartbeat = 0;
int             dailySignalCount = 0;
datetime        lastResetDate = 0;
bool            isInitialized = false;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit() {
    Print("=== Provider EA Initialization ===");
    
    // Validate input parameters
    if(!ValidateInputs()) {
        return INIT_PARAMETERS_INCORRECT;
    }
    
    // Initialize components
    if(!InitializeComponents()) {
        return INIT_FAILED;
    }
    
    // Perform initial setup
    if(!PerformInitialSetup()) {
        return INIT_FAILED;
    }
    
    // Set timer for periodic tasks
    EventSetTimer(1);
    
    isInitialized = true;
    logger.Info("Provider EA initialized successfully");
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    logger.Info(StringFormat("Provider EA stopping. Reason: %d", reason));
    
    // Send offline status
    if(isInitialized && httpClient != NULL) {
        SendProviderStatus("offline");
    }
    
    // Cleanup components
    CleanupComponents();
    
    EventKillTimer();
    Print("Provider EA stopped");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick() {
    if(!isInitialized) return;
    
    // Update signal manager with current market data
    signalManager.OnTick();
}

//+------------------------------------------------------------------+
//| Timer function                                                   |
//+------------------------------------------------------------------+
void OnTimer() {
    if(!isInitialized) return;
    
    // Reset daily counter
    ResetDailyCounterIfNeeded();
    
    // Send heartbeat
    SendHeartbeatIfNeeded();
    
    // Process pending signals
    signalManager.ProcessPendingSignals();
    
    // Check connection status
    CheckConnectionStatus();
}

//+------------------------------------------------------------------+
//| Trade event handler                                              |
//+------------------------------------------------------------------+
void OnTrade() {
    if(!isInitialized || !AUTO_SEND_SIGNALS) return;
    
    logger.Debug("Trade event detected");
    
    // Check daily limit
    if(dailySignalCount >= MAX_DAILY_SIGNALS) {
        logger.Warning("Daily signal limit reached");
        return;
    }
    
    // Process new trades
    ProcessTradeEvents();
}

//+------------------------------------------------------------------+
//| Validate input parameters                                        |
//+------------------------------------------------------------------+
bool ValidateInputs() {
    if(StringLen(API_BASE_URL) == 0) {
        Alert("API_BASE_URL is required");
        return false;
    }
    
    if(StringLen(API_KEY) == 0) {
        Alert("API_KEY is required");
        return false;
    }
    
    if(StringLen(PROVIDER_ID) == 0) {
        Alert("PROVIDER_ID is required");
        return false;
    }
    
    if(MAX_DAILY_SIGNALS <= 0) {
        Alert("MAX_DAILY_SIGNALS must be positive");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Initialize components                                            |
//+------------------------------------------------------------------+
bool InitializeComponents() {
    // Initialize logger first
    logger = new CLogger(ENABLE_LOGGING);
    if(logger == NULL) {
        Print("Failed to initialize logger");
        return false;
    }
    
    // Initialize config manager
    configManager = new CConfigManager();
    if(configManager == NULL) {
        logger.Error("Failed to initialize config manager");
        return false;
    }
    
    // Initialize HTTP client
    httpClient = new CHttpClient(API_BASE_URL, API_KEY, ENABLE_SSL);
    if(httpClient == NULL) {
        logger.Error("Failed to initialize HTTP client");
        return false;
    }
    
    // Initialize signal manager
    signalManager = new CSignalManager(httpClient, logger);
    if(signalManager == NULL) {
        logger.Error("Failed to initialize signal manager");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Perform initial setup                                           |
//+------------------------------------------------------------------+
bool PerformInitialSetup() {
    // Load configuration
    if(!configManager.LoadConfig()) {
        logger.Warning("Failed to load config, using defaults");
    }
    
    // Test API connection
    if(!TestApiConnection()) {
        logger.Error("Failed to connect to API");
        return false;
    }
    
    // Register provider
    if(!RegisterProvider()) {
        logger.Error("Failed to register provider");
        return false;
    }
    
    // Send online status
    SendProviderStatus("online");
    
    return true;
}

//+------------------------------------------------------------------+
//| Test API connection                                              |
//+------------------------------------------------------------------+
bool TestApiConnection() {
    logger.Info("Testing API connection...");
    
    string endpoint = "/api/v1/health";
    string response;
    int statusCode;
    
    if(httpClient.Get(endpoint, response, statusCode)) {
        if(statusCode == 200) {
            logger.Info("API connection successful");
            return true;
        }
    }
    
    logger.Error(StringFormat("API connection failed. Status: %d", statusCode));
    return false;
}

//+------------------------------------------------------------------+
//| Register provider with backend                                  |
//+------------------------------------------------------------------+
bool RegisterProvider() {
    logger.Info("Registering provider...");
    
    // Prepare registration data
    string jsonData = StringFormat(
        "{"
        "\"provider_id\":\"%s\","
        "\"account_number\":%d,"
        "\"account_name\":\"%s\","
        "\"broker\":\"%s\","
        "\"server\":\"%s\","
        "\"balance\":%.2f,"
        "\"equity\":%.2f,"
        "\"currency\":\"%s\","
        "\"leverage\":%d,"
        "\"timestamp\":%d"
        "}",
        PROVIDER_ID,
        AccountInfoInteger(ACCOUNT_LOGIN),
        AccountInfoString(ACCOUNT_NAME),
        AccountInfoString(ACCOUNT_COMPANY),
        AccountInfoString(ACCOUNT_SERVER),
        AccountInfoDouble(ACCOUNT_BALANCE),
        AccountInfoDouble(ACCOUNT_EQUITY),
        AccountInfoString(ACCOUNT_CURRENCY),
        AccountInfoInteger(ACCOUNT_LEVERAGE),
        TimeCurrent()
    );
    
    string endpoint = "/api/v1/providers/register";
    string response;
    int statusCode;
    
    if(httpClient.Post(endpoint, jsonData, response, statusCode)) {
        if(statusCode == 200 || statusCode == 201) {
            logger.Info("Provider registered successfully");
            return true;
        }
    }
    
    logger.Error(StringFormat("Provider registration failed. Status: %d, Response: %s", 
                             statusCode, response));
    return false;
}

//+------------------------------------------------------------------+
//| Process trade events                                             |
//+------------------------------------------------------------------+
void ProcessTradeEvents() {
    // Check for new positions
    CheckNewPositions();
    
    // Check for closed positions
    CheckClosedPositions();
    
    // Check for modified positions
    if(SEND_MODIFICATIONS) {
        CheckModifiedPositions();
    }
    
    // Check for pending orders
    if(SEND_PENDING_ORDERS) {
        CheckPendingOrders();
    }
}

//+------------------------------------------------------------------+
//| Check for new positions                                          |
//+------------------------------------------------------------------+
void CheckNewPositions() {
    static datetime lastCheck = 0;
    datetime currentTime = TimeCurrent();
    
    for(int i = PositionsTotal() - 1; i >= 0; i--) {
        if(PositionSelectByIndex(i)) {
            datetime openTime = (datetime)PositionGetInteger(POSITION_TIME);
            
            if(openTime > lastCheck) {
                ProcessNewPosition();
            }
        }
    }
    
    lastCheck = currentTime;
}

//+------------------------------------------------------------------+
//| Process new position                                             |
//+------------------------------------------------------------------+
void ProcessNewPosition() {
    ulong ticket = PositionGetInteger(POSITION_TICKET);
    string symbol = PositionGetString(POSITION_SYMBOL);
    double volume = PositionGetDouble(POSITION_VOLUME);

    // Validate signal
    if(VALIDATE_SIGNALS && !ValidateSignal(symbol, volume)) {
        logger.Warning(StringFormat("Signal validation failed for position %d", ticket));
        return;
    }

    // Create signal data
    SignalData signal;
    if(signalManager.CreateSignalFromPosition(signal)) {
        signal.provider_id = PROVIDER_ID;

        // Send signal
        if(signalManager.SendSignal(signal)) {
            dailySignalCount++;
            logger.Info(StringFormat("Signal sent for position %d", ticket));
        }
    }
}

//+------------------------------------------------------------------+
//| Check for closed positions                                       |
//+------------------------------------------------------------------+
void CheckClosedPositions() {
    datetime from = TimeCurrent() - 60; // Last minute
    datetime to = TimeCurrent();

    if(HistorySelect(from, to)) {
        int total = HistoryDealsTotal();

        for(int i = 0; i < total; i++) {
            ulong ticket = HistoryDealGetTicket(i);
            if(ticket > 0) {
                ENUM_DEAL_ENTRY entry = (ENUM_DEAL_ENTRY)HistoryDealGetInteger(ticket, DEAL_ENTRY);

                if(entry == DEAL_ENTRY_OUT) {
                    ulong positionId = HistoryDealGetInteger(ticket, DEAL_POSITION_ID);
                    double dealVolume = HistoryDealGetDouble(ticket, DEAL_VOLUME);

                    // Check if this is a partial close
                    if(IsPartialClose(positionId, dealVolume)) {
                        ProcessPartialClose(positionId, dealVolume);
                    } else {
                        ProcessClosedPosition(positionId);
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Process closed position                                          |
//+------------------------------------------------------------------+
void ProcessClosedPosition(ulong ticket) {
    SignalData signal;
    if(signalManager.CreateCloseSignal(ticket, signal)) {
        signal.provider_id = PROVIDER_ID;

        if(signalManager.SendSignal(signal)) {
            logger.Info(StringFormat("Close signal sent for position %d", ticket));
        }
    }
}

//+------------------------------------------------------------------+
//| Check for modified positions                                     |
//+------------------------------------------------------------------+
void CheckModifiedPositions() {
    // Implementation for detecting SL/TP modifications
    // This would require tracking previous values
    static double lastSL[];
    static double lastTP[];
    static double lastVolumes[];
    static ulong lastTickets[];

    // Compare current positions with stored values
    for(int i = PositionsTotal() - 1; i >= 0; i--) {
        if(PositionSelectByIndex(i)) {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            double currentSL = PositionGetDouble(POSITION_SL);
            double currentTP = PositionGetDouble(POSITION_TP);
            double currentVolume = PositionGetDouble(POSITION_VOLUME);

            // Check if this position was modified
            int index = FindTicketIndex(lastTickets, ticket);
            if(index >= 0) {
                if(MathAbs(lastSL[index] - currentSL) > 0.00001 ||
                   MathAbs(lastTP[index] - currentTP) > 0.00001) {
                    ProcessModifiedPosition(ticket);
                }

                // Check for volume change (partial close detection)
                if(MathAbs(lastVolumes[index] - currentVolume) > 0.00001) {
                    double closedVolume = lastVolumes[index] - currentVolume;
                    if(closedVolume > 0) {
                        ProcessPartialClose(ticket, closedVolume);
                    }
                }
            }

            // Update stored values
            UpdateStoredValuesWithVolume(lastTickets, lastSL, lastTP, lastVolumes,
                                       ticket, currentSL, currentTP, currentVolume);
        }
    }
}

//+------------------------------------------------------------------+
//| Process modified position                                        |
//+------------------------------------------------------------------+
void ProcessModifiedPosition(ulong ticket) {
    SignalData signal;
    if(signalManager.CreateModifySignal(ticket, signal)) {
        signal.provider_id = PROVIDER_ID;

        if(signalManager.SendSignal(signal)) {
            logger.Info(StringFormat("Modify signal sent for position %d", ticket));
        }
    }
}

//+------------------------------------------------------------------+
//| Validate signal                                                  |
//+------------------------------------------------------------------+
bool ValidateSignal(string symbol, double volume) {
    // Check volume limits
    if(volume < MIN_VOLUME || volume > MAX_SIGNAL_VOLUME) {
        return false;
    }

    // Check symbol validity
    if(!SymbolSelect(symbol, false)) {
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Reset daily counter if needed                                   |
//+------------------------------------------------------------------+
void ResetDailyCounterIfNeeded() {
    datetime currentDate = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));

    if(currentDate > lastResetDate) {
        dailySignalCount = 0;
        lastResetDate = currentDate;
        logger.Info("Daily signal counter reset");
    }
}

//+------------------------------------------------------------------+
//| Send heartbeat if needed                                         |
//+------------------------------------------------------------------+
void SendHeartbeatIfNeeded() {
    if(TimeCurrent() - lastHeartbeat >= HEARTBEAT_INTERVAL) {
        SendHeartbeat();
        lastHeartbeat = TimeCurrent();
    }
}

//+------------------------------------------------------------------+
//| Send heartbeat to server                                         |
//+------------------------------------------------------------------+
void SendHeartbeat() {
    string jsonData = StringFormat(
        "{"
        "\"provider_id\":\"%s\","
        "\"status\":\"online\","
        "\"balance\":%.2f,"
        "\"equity\":%.2f,"
        "\"margin\":%.2f,"
        "\"free_margin\":%.2f,"
        "\"timestamp\":%d"
        "}",
        PROVIDER_ID,
        AccountInfoDouble(ACCOUNT_BALANCE),
        AccountInfoDouble(ACCOUNT_EQUITY),
        AccountInfoDouble(ACCOUNT_MARGIN),
        AccountInfoDouble(ACCOUNT_MARGIN_FREE),
        TimeCurrent()
    );

    string endpoint = "/api/v1/providers/heartbeat";
    string response;
    int statusCode;

    if(httpClient.Post(endpoint, jsonData, response, statusCode)) {
        if(statusCode == 200) {
            logger.Debug("Heartbeat sent successfully");
        }
    }
}

//+------------------------------------------------------------------+
//| Send provider status                                             |
//+------------------------------------------------------------------+
void SendProviderStatus(string status) {
    string jsonData = StringFormat(
        "{"
        "\"provider_id\":\"%s\","
        "\"status\":\"%s\","
        "\"timestamp\":%d"
        "}",
        PROVIDER_ID,
        status,
        TimeCurrent()
    );

    string endpoint = "/api/v1/providers/status";
    string response;
    int statusCode;

    httpClient.Post(endpoint, jsonData, response, statusCode);
}

//+------------------------------------------------------------------+
//| Check connection status                                          |
//+------------------------------------------------------------------+
void CheckConnectionStatus() {
    static datetime lastConnectionCheck = 0;

    if(TimeCurrent() - lastConnectionCheck >= 60) { // Check every minute
        if(!TerminalInfoInteger(TERMINAL_CONNECTED)) {
            logger.Warning("Terminal not connected to internet");
        }

        lastConnectionCheck = TimeCurrent();
    }
}

//+------------------------------------------------------------------+
//| Cleanup components                                               |
//+------------------------------------------------------------------+
void CleanupComponents() {
    if(httpClient != NULL) {
        delete httpClient;
        httpClient = NULL;
    }

    if(signalManager != NULL) {
        delete signalManager;
        signalManager = NULL;
    }

    if(configManager != NULL) {
        delete configManager;
        configManager = NULL;
    }

    if(logger != NULL) {
        delete logger;
        logger = NULL;
    }
}

//+------------------------------------------------------------------+
//| Helper functions                                                 |
//+------------------------------------------------------------------+
int FindTicketIndex(ulong &tickets[], ulong ticket) {
    for(int i = 0; i < ArraySize(tickets); i++) {
        if(tickets[i] == ticket) return i;
    }
    return -1;
}

void UpdateStoredValues(ulong &tickets[], double &sl[], double &tp[],
                       ulong ticket, double newSL, double newTP) {
    int index = FindTicketIndex(tickets, ticket);

    if(index < 0) {
        // Add new ticket
        int size = ArraySize(tickets);
        ArrayResize(tickets, size + 1);
        ArrayResize(sl, size + 1);
        ArrayResize(tp, size + 1);

        tickets[size] = ticket;
        sl[size] = newSL;
        tp[size] = newTP;
    } else {
        // Update existing
        sl[index] = newSL;
        tp[index] = newTP;
    }
}

void UpdateStoredValuesWithVolume(ulong &tickets[], double &sl[], double &tp[], double &volumes[],
                                 ulong ticket, double newSL, double newTP, double newVolume) {
    int index = FindTicketIndex(tickets, ticket);

    if(index < 0) {
        // Add new ticket
        int size = ArraySize(tickets);
        ArrayResize(tickets, size + 1);
        ArrayResize(sl, size + 1);
        ArrayResize(tp, size + 1);
        ArrayResize(volumes, size + 1);

        tickets[size] = ticket;
        sl[size] = newSL;
        tp[size] = newTP;
        volumes[size] = newVolume;
    } else {
        // Update existing
        sl[index] = newSL;
        tp[index] = newTP;
        volumes[index] = newVolume;
    }
}

//+------------------------------------------------------------------+
//| Check if position close is partial                              |
//+------------------------------------------------------------------+
bool IsPartialClose(ulong positionId, double dealVolume) {
    // Select position by ID to get original volume
    if(PositionSelectByTicket(positionId)) {
        double currentVolume = PositionGetDouble(POSITION_VOLUME);
        return currentVolume > 0; // Position still exists = partial close
    }

    // If position doesn't exist, check history for original volume
    if(HistorySelectByPosition(positionId)) {
        double totalVolume = 0;
        int totalDeals = HistoryDealsTotal();

        for(int i = 0; i < totalDeals; i++) {
            ulong dealTicket = HistoryDealGetTicket(i);
            if(HistoryDealGetInteger(dealTicket, DEAL_POSITION_ID) == positionId) {
                ENUM_DEAL_ENTRY entry = (ENUM_DEAL_ENTRY)HistoryDealGetInteger(dealTicket, DEAL_ENTRY);
                double volume = HistoryDealGetDouble(dealTicket, DEAL_VOLUME);

                if(entry == DEAL_ENTRY_IN) {
                    totalVolume += volume;
                } else if(entry == DEAL_ENTRY_OUT) {
                    totalVolume -= volume;
                }
            }
        }

        return totalVolume > 0; // Still has remaining volume
    }

    return false;
}

//+------------------------------------------------------------------+
//| Process partial close                                            |
//+------------------------------------------------------------------+
void ProcessPartialClose(ulong positionId, double closedVolume) {
    logger.Info(StringFormat("Processing partial close for position %d, volume: %.2f",
                            positionId, closedVolume));

    // Create partial close signal using helper
    SignalData signal;
    if(!CSignalDataHelper::CreatePartialCloseSignal(positionId, closedVolume, signal, PROVIDER_ID)) {
        logger.Error(StringFormat("Failed to create partial close signal for position %d", positionId));
        return;
    }

    // Validate signal
    if(!CSignalDataHelper::ValidateSignal(signal)) {
        logger.Error("Partial close signal validation failed");
        return;
    }

    // Send signal
    if(signalManager.SendSignal(signal)) {
        dailySignalCount++;
        logger.Info(StringFormat("Partial close signal sent for position %d", positionId));
    } else {
        logger.Error(StringFormat("Failed to send partial close signal for position %d", positionId));
    }
}

//+------------------------------------------------------------------+
//| Error handling and recovery                                     |
//+------------------------------------------------------------------+
void HandleCriticalError(string errorMessage) {
    logger.Critical(errorMessage);

    // Send error notification to server
    SendErrorNotification(errorMessage);

    // Disable auto signal sending temporarily
    AUTO_SEND_SIGNALS = false;

    // Set flag for manual intervention
    GlobalVariableSet("EA_ERROR_STATE", 1);

    Alert("CRITICAL ERROR: " + errorMessage + " - EA disabled");
}

void SendErrorNotification(string errorMessage) {
    string jsonData = StringFormat(
        "{"
        "\"type\":\"error\","
        "\"provider_id\":\"%s\","
        "\"error_message\":\"%s\","
        "\"timestamp\":%d,"
        "\"account\":%d"
        "}",
        PROVIDER_ID,
        errorMessage,
        TimeCurrent(),
        AccountInfoInteger(ACCOUNT_LOGIN)
    );

    string endpoint = "/api/v1/providers/error";
    string response;
    int statusCode;

    httpClient.Post(endpoint, jsonData, response, statusCode);
}

bool RecoverFromError() {
    // Check if error state is set
    if(GlobalVariableCheck("EA_ERROR_STATE")) {
        double errorState = GlobalVariableGet("EA_ERROR_STATE");
        if(errorState > 0) {
            // Try to recover
            if(TestApiConnection()) {
                // Recovery successful
                GlobalVariableDel("EA_ERROR_STATE");
                AUTO_SEND_SIGNALS = true;
                logger.Info("Recovered from error state");
                return true;
            }
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Network retry mechanism                                         |
//+------------------------------------------------------------------+
bool SendSignalWithRetry(SignalData &signal, int maxRetries = 3) {
    for(int attempt = 1; attempt <= maxRetries; attempt++) {
        if(signalManager.SendSignal(signal)) {
            return true;
        }

        logger.Warning(StringFormat("Signal send attempt %d failed", attempt));

        if(attempt < maxRetries) {
            Sleep(RETRY_DELAY * attempt); // Exponential backoff
        }
    }

    logger.Error(StringFormat("Failed to send signal after %d attempts", maxRetries));
    return false;
}

//+------------------------------------------------------------------+
//| Position validation                                              |
//+------------------------------------------------------------------+
bool ValidatePositionData(ulong ticket) {
    if(!PositionSelectByTicket(ticket)) {
        logger.Warning(StringFormat("Position %d not found", ticket));
        return false;
    }

    string symbol = PositionGetString(POSITION_SYMBOL);
    double volume = PositionGetDouble(POSITION_VOLUME);
    double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);

    // Validate symbol
    if(StringLen(symbol) < 3) {
        logger.Warning(StringFormat("Invalid symbol for position %d: %s", ticket, symbol));
        return false;
    }

    // Validate volume
    if(volume <= 0 || volume > MAX_SIGNAL_VOLUME) {
        logger.Warning(StringFormat("Invalid volume for position %d: %.2f", ticket, volume));
        return false;
    }

    // Validate price
    if(openPrice <= 0) {
        logger.Warning(StringFormat("Invalid open price for position %d: %.5f", ticket, openPrice));
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Market hours validation                                          |
//+------------------------------------------------------------------+
bool IsMarketOpen(string symbol) {
    // Check if symbol is tradeable
    if(!SymbolInfoInteger(symbol, SYMBOL_TRADE_MODE)) {
        return false;
    }

    // Check trading sessions
    datetime currentTime = TimeCurrent();
    MqlDateTime dt;
    TimeToStruct(currentTime, dt);

    // Basic market hours check (can be enhanced)
    int hour = dt.hour;
    int dayOfWeek = dt.day_of_week;

    // Skip weekends
    if(dayOfWeek == 0 || dayOfWeek == 6) {
        return false;
    }

    // Skip major holidays (simplified)
    if(IsHoliday(currentTime)) {
        return false;
    }

    return true;
}

bool IsHoliday(datetime checkTime) {
    // Simplified holiday check
    MqlDateTime dt;
    TimeToStruct(checkTime, dt);

    // New Year's Day
    if(dt.mon == 1 && dt.day == 1) return true;

    // Christmas
    if(dt.mon == 12 && dt.day == 25) return true;

    // Add more holidays as needed

    return false;
}

//+------------------------------------------------------------------+
//| Performance monitoring                                           |
//+------------------------------------------------------------------+
void MonitorPerformance() {
    static datetime lastPerformanceCheck = 0;

    if(TimeCurrent() - lastPerformanceCheck < 300) return; // Check every 5 minutes

    // Check memory usage
    long memoryUsed = TerminalInfoInteger(TERMINAL_MEMORY_USED);
    if(memoryUsed > 100 * 1024 * 1024) { // 100MB
        logger.Warning(StringFormat("High memory usage: %d MB", memoryUsed / (1024 * 1024)));
    }

    // Check CPU usage (simplified)
    static int lastTickCount = 0;
    int currentTickCount = GetTickCount();

    if(lastTickCount > 0) {
        int timeDiff = currentTickCount - lastTickCount;
        if(timeDiff > 5000) { // More than 5 seconds between ticks
            logger.Warning("Possible performance issue detected");
        }
    }

    lastTickCount = currentTickCount;
    lastPerformanceCheck = TimeCurrent();
}

//+------------------------------------------------------------------+
//| Configuration validation                                         |
//+------------------------------------------------------------------+
bool ValidateConfiguration() {
    // Check API configuration
    if(StringLen(API_BASE_URL) == 0) {
        logger.Error("API_BASE_URL not configured");
        return false;
    }

    if(StringLen(API_KEY) == 0) {
        logger.Error("API_KEY not configured");
        return false;
    }

    if(StringLen(PROVIDER_ID) == 0) {
        logger.Error("PROVIDER_ID not configured");
        return false;
    }

    // Check risk parameters
    if(MAX_DAILY_SIGNALS <= 0) {
        logger.Error("Invalid MAX_DAILY_SIGNALS");
        return false;
    }

    if(MAX_SIGNAL_VOLUME <= 0) {
        logger.Error("Invalid MAX_SIGNAL_VOLUME");
        return false;
    }

    // Check account permissions
    if(!AccountInfoInteger(ACCOUNT_TRADE_ALLOWED)) {
        logger.Error("Trading not allowed on this account");
        return false;
    }

    if(!AccountInfoInteger(ACCOUNT_TRADE_EXPERT)) {
        logger.Error("Expert Advisor trading not allowed");
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Graceful shutdown                                                |
//+------------------------------------------------------------------+
void GracefulShutdown() {
    logger.Info("Initiating graceful shutdown...");

    // Send offline status
    SendProviderStatus("offline");

    // Save current state
    SaveEAState();

    // Wait for pending operations
    Sleep(1000);

    logger.Info("Graceful shutdown completed");
}

void SaveEAState() {
    // Save important state variables to global variables
    GlobalVariableSet("EA_DAILY_SIGNAL_COUNT", dailySignalCount);
    GlobalVariableSet("EA_LAST_RESET_DATE", lastResetDate);
    GlobalVariableSet("EA_LAST_HEARTBEAT", lastHeartbeat);

    logger.Info("EA state saved");
}

void LoadEAState() {
    // Load state variables from global variables
    if(GlobalVariableCheck("EA_DAILY_SIGNAL_COUNT")) {
        dailySignalCount = (int)GlobalVariableGet("EA_DAILY_SIGNAL_COUNT");
    }

    if(GlobalVariableCheck("EA_LAST_RESET_DATE")) {
        lastResetDate = (datetime)GlobalVariableGet("EA_LAST_RESET_DATE");
    }

    if(GlobalVariableCheck("EA_LAST_HEARTBEAT")) {
        lastHeartbeat = (datetime)GlobalVariableGet("EA_LAST_HEARTBEAT");
    }

    logger.Info("EA state loaded");
}

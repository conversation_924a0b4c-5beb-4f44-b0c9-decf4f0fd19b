//+------------------------------------------------------------------+
//| FollowerEA.mq5                                                   |
//| Professional Copy Trading Follower EA                           |
//| Receives and executes trading signals from providers            |
//+------------------------------------------------------------------+
#property copyright "Copy Trading System"
#property version   "2.00"
#property description "Follower EA for Copy Trading System"

#include "../includes/SignalData.mqh"
#include "includes/WebSocketClient.mqh"
#include "includes/TradeExecutor.mqh"
#include "includes/RiskManager.mqh"
#include "includes/Logger.mqh"

//--- Input parameters
input group "=== API Configuration ==="
input string    WS_URL = "wss://api.copytrading.com/ws/signals";
input string    API_BASE_URL = "https://api.copytrading.com";
input string    API_KEY = "";
input string    FOLLOWER_ID = "";
input bool      ENABLE_SSL = true;

input group "=== Provider Settings ==="
input string    ALLOWED_PROVIDERS = ""; // Comma separated provider IDs
input bool      AUTO_COPY_ENABLED = true;
input double    COPY_RATIO = 1.0;       // 1.0 = 100% copy ratio

input group "=== Risk Management ==="
input double    RISK_PERCENT = 2.0;     // Risk per trade (%)
input double    MAX_LOT_SIZE = 1.0;     // Maximum lot size
input double    MIN_LOT_SIZE = 0.01;    // Minimum lot size
input double    MAX_DAILY_LOSS = 500.0; // Maximum daily loss in account currency
input double    MAX_DRAWDOWN = 20.0;    // Maximum drawdown (%)
input bool      USE_STOP_LOSS = true;
input bool      USE_TAKE_PROFIT = true;

input group "=== Position Management ==="
input bool      CLOSE_ON_SIGNAL = true;  // Close positions when provider closes
input bool      MODIFY_ON_SIGNAL = true; // Modify SL/TP when provider modifies
input int       MAX_OPEN_POSITIONS = 10; // Maximum open positions
input double    MIN_FREE_MARGIN = 100.0; // Minimum free margin required

input group "=== System Settings ==="
input int       RECONNECT_INTERVAL = 5;  // WebSocket reconnect interval (seconds)
input int       HEARTBEAT_INTERVAL = 30; // Heartbeat interval (seconds)
input bool      ENABLE_LOGGING = true;
input bool      SEND_EXECUTION_REPORTS = true;

//--- Global variables
CWebSocketClient* wsClient;
CTradeExecutor*   tradeExecutor;
CRiskManager*     riskManager;
CLogger*          logger;

datetime          lastHeartbeat = 0;
double            dailyLoss = 0.0;
datetime          lastResetDate = 0;
bool              isInitialized = false;
string            allowedProviders[];

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit() {
    Print("=== Follower EA Initialization ===");
    
    // Validate input parameters
    if(!ValidateInputs()) {
        return INIT_PARAMETERS_INCORRECT;
    }
    
    // Parse allowed providers
    ParseAllowedProviders();
    
    // Initialize components
    if(!InitializeComponents()) {
        return INIT_FAILED;
    }
    
    // Perform initial setup
    if(!PerformInitialSetup()) {
        return INIT_FAILED;
    }
    
    // Set timer for periodic tasks
    EventSetTimer(1);
    
    isInitialized = true;
    logger.Info("Follower EA initialized successfully");
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    logger.Info(StringFormat("Follower EA stopping. Reason: %d", reason));
    
    // Send offline status
    if(isInitialized) {
        SendFollowerStatus("offline");
    }
    
    // Cleanup components
    CleanupComponents();
    
    EventKillTimer();
    Print("Follower EA stopped");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick() {
    if(!isInitialized) return;
    
    // Update risk manager with current market data
    riskManager.OnTick();
    
    // Update trade executor
    tradeExecutor.OnTick();
}

//+------------------------------------------------------------------+
//| Timer function                                                   |
//+------------------------------------------------------------------+
void OnTimer() {
    if(!isInitialized) return;
    
    // Reset daily loss counter
    ResetDailyLossIfNeeded();
    
    // Check WebSocket connection
    CheckWebSocketConnection();
    
    // Send heartbeat
    SendHeartbeatIfNeeded();
    
    // Check risk limits
    CheckRiskLimits();
    
    // Process pending executions
    tradeExecutor.ProcessPendingExecutions();
}

//+------------------------------------------------------------------+
//| Trade event handler                                              |
//+------------------------------------------------------------------+
void OnTrade() {
    if(!isInitialized) return;
    
    logger.Debug("Trade event detected");
    
    // Update daily loss calculation
    UpdateDailyLoss();
    
    // Send execution reports if enabled
    if(SEND_EXECUTION_REPORTS) {
        SendExecutionReports();
    }
}

//+------------------------------------------------------------------+
//| Validate input parameters                                        |
//+------------------------------------------------------------------+
bool ValidateInputs() {
    if(StringLen(WS_URL) == 0) {
        Alert("WS_URL is required");
        return false;
    }
    
    if(StringLen(API_KEY) == 0) {
        Alert("API_KEY is required");
        return false;
    }
    
    if(StringLen(FOLLOWER_ID) == 0) {
        Alert("FOLLOWER_ID is required");
        return false;
    }
    
    if(RISK_PERCENT <= 0 || RISK_PERCENT > 100) {
        Alert("RISK_PERCENT must be between 0 and 100");
        return false;
    }
    
    if(COPY_RATIO <= 0) {
        Alert("COPY_RATIO must be positive");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Parse allowed providers                                          |
//+------------------------------------------------------------------+
void ParseAllowedProviders() {
    if(StringLen(ALLOWED_PROVIDERS) == 0) {
        logger.Info("No provider filter set - will copy from all providers");
        return;
    }
    
    string providers = ALLOWED_PROVIDERS;
    int count = 0;
    
    while(StringFind(providers, ",") >= 0) {
        int pos = StringFind(providers, ",");
        ArrayResize(allowedProviders, count + 1);
        allowedProviders[count] = StringTrimLeft(StringTrimRight(StringSubstr(providers, 0, pos)));
        providers = StringSubstr(providers, pos + 1);
        count++;
    }
    
    if(StringLen(providers) > 0) {
        ArrayResize(allowedProviders, count + 1);
        allowedProviders[count] = StringTrimLeft(StringTrimRight(providers));
    }
    
    logger.Info(StringFormat("Allowed providers configured: %d", ArraySize(allowedProviders)));
}

//+------------------------------------------------------------------+
//| Initialize components                                            |
//+------------------------------------------------------------------+
bool InitializeComponents() {
    // Initialize logger first
    logger = new CLogger(ENABLE_LOGGING);
    if(logger == NULL) {
        Print("Failed to initialize logger");
        return false;
    }
    
    // Initialize risk manager
    riskManager = new CRiskManager(logger);
    if(riskManager == NULL) {
        logger.Error("Failed to initialize risk manager");
        return false;
    }
    
    // Configure risk manager
    riskManager.SetRiskPercent(RISK_PERCENT);
    riskManager.SetMaxLotSize(MAX_LOT_SIZE);
    riskManager.SetMinLotSize(MIN_LOT_SIZE);
    riskManager.SetMaxDailyLoss(MAX_DAILY_LOSS);
    riskManager.SetMaxDrawdown(MAX_DRAWDOWN);
    
    // Initialize trade executor
    tradeExecutor = new CTradeExecutor(riskManager, logger);
    if(tradeExecutor == NULL) {
        logger.Error("Failed to initialize trade executor");
        return false;
    }
    
    // Configure trade executor
    tradeExecutor.SetCopyRatio(COPY_RATIO);
    tradeExecutor.SetMaxOpenPositions(MAX_OPEN_POSITIONS);
    tradeExecutor.SetMinFreeMargin(MIN_FREE_MARGIN);
    tradeExecutor.SetUseStopLoss(USE_STOP_LOSS);
    tradeExecutor.SetUseTakeProfit(USE_TAKE_PROFIT);
    
    // Initialize WebSocket client
    wsClient = new CWebSocketClient(WS_URL, API_KEY, logger);
    if(wsClient == NULL) {
        logger.Error("Failed to initialize WebSocket client");
        return false;
    }
    
    // Set signal callback
    wsClient.SetSignalCallback(OnSignalReceived);
    
    return true;
}

//+------------------------------------------------------------------+
//| Perform initial setup                                           |
//+------------------------------------------------------------------+
bool PerformInitialSetup() {
    // Test API connection
    if(!TestApiConnection()) {
        logger.Error("Failed to connect to API");
        return false;
    }
    
    // Register follower
    if(!RegisterFollower()) {
        logger.Error("Failed to register follower");
        return false;
    }
    
    // Connect to WebSocket
    if(!wsClient.Connect()) {
        logger.Error("Failed to connect to WebSocket");
        return false;
    }
    
    // Send online status
    SendFollowerStatus("online");
    
    return true;
}

//+------------------------------------------------------------------+
//| Test API connection                                              |
//+------------------------------------------------------------------+
bool TestApiConnection() {
    logger.Info("Testing API connection...");
    
    // Simple HTTP client for testing
    char data[];
    char result[];
    string headers = "Authorization: Bearer " + API_KEY + "\r\n";
    string resultHeaders;
    
    int statusCode = WebRequest("GET", API_BASE_URL + "/api/v1/health", 
                               headers, 5000, data, result, resultHeaders);
    
    if(statusCode == 200) {
        logger.Info("API connection successful");
        return true;
    }
    
    logger.Error(StringFormat("API connection failed. Status: %d", statusCode));
    return false;
}

//+------------------------------------------------------------------+
//| Register follower with backend                                  |
//+------------------------------------------------------------------+
bool RegisterFollower() {
    logger.Info("Registering follower...");
    
    // Prepare registration data
    string jsonData = StringFormat(
        "{"
        "\"follower_id\":\"%s\","
        "\"account_number\":%d,"
        "\"account_name\":\"%s\","
        "\"broker\":\"%s\","
        "\"server\":\"%s\","
        "\"balance\":%.2f,"
        "\"equity\":%.2f,"
        "\"currency\":\"%s\","
        "\"leverage\":%d,"
        "\"risk_percent\":%.2f,"
        "\"copy_ratio\":%.2f,"
        "\"allowed_providers\":\"%s\","
        "\"timestamp\":%d"
        "}",
        FOLLOWER_ID,
        AccountInfoInteger(ACCOUNT_LOGIN),
        AccountInfoString(ACCOUNT_NAME),
        AccountInfoString(ACCOUNT_COMPANY),
        AccountInfoString(ACCOUNT_SERVER),
        AccountInfoDouble(ACCOUNT_BALANCE),
        AccountInfoDouble(ACCOUNT_EQUITY),
        AccountInfoString(ACCOUNT_CURRENCY),
        AccountInfoInteger(ACCOUNT_LEVERAGE),
        RISK_PERCENT,
        COPY_RATIO,
        ALLOWED_PROVIDERS,
        TimeCurrent()
    );
    
    char postData[];
    char result[];
    string headers = "Content-Type: application/json\r\n";
    headers += "Authorization: Bearer " + API_KEY + "\r\n";
    string resultHeaders;
    
    StringToCharArray(jsonData, postData, 0, StringLen(jsonData));
    
    int statusCode = WebRequest("POST", API_BASE_URL + "/api/v1/followers/register",
                               headers, 5000, postData, result, resultHeaders);
    
    if(statusCode == 200 || statusCode == 201) {
        logger.Info("Follower registered successfully");
        return true;
    }
    
    logger.Error(StringFormat("Follower registration failed. Status: %d, Response: %s",
                             statusCode, CharArrayToString(result)));
    return false;
}

//+------------------------------------------------------------------+
//| Signal received callback                                         |
//+------------------------------------------------------------------+
void OnSignalReceived(string signalJson) {
    logger.Debug(StringFormat("Signal received: %s", signalJson));

    // Parse signal
    SignalData signal;
    if(!signal.FromJson(signalJson)) {
        logger.Error("Failed to parse signal JSON");
        return;
    }

    // Check if provider is allowed
    if(!IsProviderAllowed(signal.provider_id)) {
        logger.Debug(StringFormat("Provider not allowed: %s", signal.provider_id));
        return;
    }

    // Check if auto copy is enabled
    if(!AUTO_COPY_ENABLED) {
        logger.Info("Auto copy disabled, signal ignored");
        return;
    }

    // Add to execution queue
    tradeExecutor.AddToQueue(signal);
}

//+------------------------------------------------------------------+
//| Check if provider is allowed                                    |
//+------------------------------------------------------------------+
bool IsProviderAllowed(string providerId) {
    if(ArraySize(allowedProviders) == 0) {
        return true; // No filter set, allow all
    }

    for(int i = 0; i < ArraySize(allowedProviders); i++) {
        if(allowedProviders[i] == providerId) {
            return true;
        }
    }

    return false;
}

//+------------------------------------------------------------------+
//| Check WebSocket connection                                       |
//+------------------------------------------------------------------+
void CheckWebSocketConnection() {
    if(wsClient != NULL) {
        wsClient.CheckMessages();

        if(!wsClient.IsConnected()) {
            logger.Warning("WebSocket disconnected, attempting reconnection");
        }
    }
}

//+------------------------------------------------------------------+
//| Send heartbeat if needed                                         |
//+------------------------------------------------------------------+
void SendHeartbeatIfNeeded() {
    if(TimeCurrent() - lastHeartbeat >= HEARTBEAT_INTERVAL) {
        SendHeartbeat();
        lastHeartbeat = TimeCurrent();
    }
}

//+------------------------------------------------------------------+
//| Send heartbeat to server                                         |
//+------------------------------------------------------------------+
void SendHeartbeat() {
    string jsonData = StringFormat(
        "{"
        "\"follower_id\":\"%s\","
        "\"status\":\"online\","
        "\"balance\":%.2f,"
        "\"equity\":%.2f,"
        "\"margin\":%.2f,"
        "\"free_margin\":%.2f,"
        "\"open_positions\":%d,"
        "\"daily_pnl\":%.2f,"
        "\"drawdown\":%.2f,"
        "\"timestamp\":%d"
        "}",
        FOLLOWER_ID,
        AccountInfoDouble(ACCOUNT_BALANCE),
        AccountInfoDouble(ACCOUNT_EQUITY),
        AccountInfoDouble(ACCOUNT_MARGIN),
        AccountInfoDouble(ACCOUNT_MARGIN_FREE),
        riskManager.GetOpenPositionsCount(),
        riskManager.GetDailyPnL(),
        riskManager.GetCurrentDrawdown(),
        TimeCurrent()
    );

    char postData[];
    char result[];
    string headers = "Content-Type: application/json\r\n";
    headers += "Authorization: Bearer " + API_KEY + "\r\n";
    string resultHeaders;

    StringToCharArray(jsonData, postData, 0, StringLen(jsonData));

    int statusCode = WebRequest("POST", API_BASE_URL + "/api/v1/followers/heartbeat",
                               headers, 3000, postData, result, resultHeaders);

    if(statusCode == 200) {
        logger.Debug("Heartbeat sent successfully");
    } else {
        logger.Warning(StringFormat("Heartbeat failed. Status: %d", statusCode));
    }
}

//+------------------------------------------------------------------+
//| Send follower status                                             |
//+------------------------------------------------------------------+
void SendFollowerStatus(string status) {
    string jsonData = StringFormat(
        "{"
        "\"follower_id\":\"%s\","
        "\"status\":\"%s\","
        "\"timestamp\":%d"
        "}",
        FOLLOWER_ID,
        status,
        TimeCurrent()
    );

    char postData[];
    char result[];
    string headers = "Content-Type: application/json\r\n";
    headers += "Authorization: Bearer " + API_KEY + "\r\n";
    string resultHeaders;

    StringToCharArray(jsonData, postData, 0, StringLen(jsonData));

    WebRequest("POST", API_BASE_URL + "/api/v1/followers/status",
               headers, 3000, postData, result, resultHeaders);
}

//+------------------------------------------------------------------+
//| Check risk limits                                                |
//+------------------------------------------------------------------+
void CheckRiskLimits() {
    if(!riskManager.CheckRiskLimits()) {
        logger.Warning("Risk limits exceeded - disabling auto copy");
        // Could implement emergency position closure here
    }
}

//+------------------------------------------------------------------+
//| Reset daily loss if needed                                      |
//+------------------------------------------------------------------+
void ResetDailyLossIfNeeded() {
    datetime currentDate = StringToTime(TimeToString(TimeCurrent(), TIME_DATE));

    if(currentDate > lastResetDate) {
        dailyLoss = 0.0;
        lastResetDate = currentDate;
        logger.Info("Daily loss counter reset");
    }
}

//+------------------------------------------------------------------+
//| Update daily loss                                                |
//+------------------------------------------------------------------+
void UpdateDailyLoss() {
    // This would be called from OnTrade to track daily P&L
    double currentPnL = riskManager.GetDailyPnL();
    if(currentPnL < 0) {
        dailyLoss = MathAbs(currentPnL);
    }
}

//+------------------------------------------------------------------+
//| Send execution reports                                           |
//+------------------------------------------------------------------+
void SendExecutionReports() {
    // Implementation to send execution results back to server
    // This would track recent trades and report their outcomes
    static datetime lastReport = 0;

    if(TimeCurrent() - lastReport < 60) return; // Report every minute

    // Get recent trade history and send reports
    datetime from = lastReport > 0 ? lastReport : TimeCurrent() - 300;
    datetime to = TimeCurrent();

    if(HistorySelect(from, to)) {
        int total = HistoryDealsTotal();

        for(int i = 0; i < total; i++) {
            ulong ticket = HistoryDealGetTicket(i);
            if(ticket > 0) {
                SendExecutionReport(ticket);
            }
        }
    }

    lastReport = TimeCurrent();
}

//+------------------------------------------------------------------+
//| Send individual execution report                                 |
//+------------------------------------------------------------------+
void SendExecutionReport(ulong ticket) {
    if(!HistoryDealSelect(ticket)) return;

    string jsonData = StringFormat(
        "{"
        "\"follower_id\":\"%s\","
        "\"ticket\":%d,"
        "\"symbol\":\"%s\","
        "\"type\":\"%s\","
        "\"volume\":%.2f,"
        "\"price\":%.5f,"
        "\"profit\":%.2f,"
        "\"commission\":%.2f,"
        "\"swap\":%.2f,"
        "\"entry\":\"%s\","
        "\"time\":%d,"
        "\"timestamp\":%d"
        "}",
        FOLLOWER_ID,
        ticket,
        HistoryDealGetString(ticket, DEAL_SYMBOL),
        HistoryDealGetInteger(ticket, DEAL_TYPE) == DEAL_TYPE_BUY ? "buy" : "sell",
        HistoryDealGetDouble(ticket, DEAL_VOLUME),
        HistoryDealGetDouble(ticket, DEAL_PRICE),
        HistoryDealGetDouble(ticket, DEAL_PROFIT),
        HistoryDealGetDouble(ticket, DEAL_COMMISSION),
        HistoryDealGetDouble(ticket, DEAL_SWAP),
        HistoryDealGetInteger(ticket, DEAL_ENTRY) == DEAL_ENTRY_IN ? "in" : "out",
        HistoryDealGetInteger(ticket, DEAL_TIME),
        TimeCurrent()
    );

    char postData[];
    char result[];
    string headers = "Content-Type: application/json\r\n";
    headers += "Authorization: Bearer " + API_KEY + "\r\n";
    string resultHeaders;

    StringToCharArray(jsonData, postData, 0, StringLen(jsonData));

    WebRequest("POST", API_BASE_URL + "/api/v1/executions/report",
               headers, 3000, postData, result, resultHeaders);
}

//+------------------------------------------------------------------+
//| Cleanup components                                               |
//+------------------------------------------------------------------+
void CleanupComponents() {
    if(wsClient != NULL) {
        delete wsClient;
        wsClient = NULL;
    }

    if(tradeExecutor != NULL) {
        delete tradeExecutor;
        tradeExecutor = NULL;
    }

    if(riskManager != NULL) {
        delete riskManager;
        riskManager = NULL;
    }

    if(logger != NULL) {
        delete logger;
        logger = NULL;
    }
}

//+------------------------------------------------------------------+
//| Error handling and recovery                                     |
//+------------------------------------------------------------------+
void HandleCriticalError(string errorMessage) {
    logger.Critical(errorMessage);

    // Send error notification to server
    SendErrorNotification(errorMessage);

    // Disable auto copy temporarily
    AUTO_COPY_ENABLED = false;

    // Set error state
    GlobalVariableSet("FOLLOWER_ERROR_STATE", 1);

    Alert("CRITICAL ERROR: " + errorMessage + " - Auto copy disabled");
}

void SendErrorNotification(string errorMessage) {
    string jsonData = StringFormat(
        "{"
        "\"type\":\"error\","
        "\"follower_id\":\"%s\","
        "\"error_message\":\"%s\","
        "\"timestamp\":%d,"
        "\"account\":%d"
        "}",
        FOLLOWER_ID,
        errorMessage,
        TimeCurrent(),
        AccountInfoInteger(ACCOUNT_LOGIN)
    );

    char postData[];
    char result[];
    string headers = "Content-Type: application/json\r\n";
    headers += "Authorization: Bearer " + API_KEY + "\r\n";
    string resultHeaders;

    StringToCharArray(jsonData, postData, 0, StringLen(jsonData));

    WebRequest("POST", API_BASE_URL + "/api/v1/followers/error",
               headers, 3000, postData, result, resultHeaders);
}

bool RecoverFromError() {
    // Check if error state is set
    if(GlobalVariableCheck("FOLLOWER_ERROR_STATE")) {
        double errorState = GlobalVariableGet("FOLLOWER_ERROR_STATE");
        if(errorState > 0) {
            // Try to recover
            if(TestApiConnection()) {
                // Recovery successful
                GlobalVariableDel("FOLLOWER_ERROR_STATE");
                AUTO_COPY_ENABLED = true;
                logger.Info("Recovered from error state");
                return true;
            }
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Connection monitoring and recovery                              |
//+------------------------------------------------------------------+
void MonitorConnections() {
    static datetime lastConnectionCheck = 0;

    if(TimeCurrent() - lastConnectionCheck < 30) return; // Check every 30 seconds

    // Check WebSocket connection
    if(wsClient != NULL && !wsClient.IsConnected()) {
        logger.Warning("WebSocket disconnected, attempting reconnection");

        // Try to reconnect
        if(!wsClient.Connect()) {
            logger.Error("Failed to reconnect WebSocket");

            // Fallback to HTTP polling
            EnableHttpPollingMode();
        }
    }

    // Check internet connection
    if(!TerminalInfoInteger(TERMINAL_CONNECTED)) {
        logger.Warning("Terminal not connected to internet");
        HandleNetworkDisconnection();
    }

    lastConnectionCheck = TimeCurrent();
}

void EnableHttpPollingMode() {
    logger.Info("Switching to HTTP polling mode");

    // Set polling flag
    GlobalVariableSet("HTTP_POLLING_MODE", 1);

    // Start polling timer
    EventSetTimer(1); // Poll every second
}

void HandleNetworkDisconnection() {
    // Pause auto copy during network issues
    bool wasAutoCopyEnabled = AUTO_COPY_ENABLED;
    AUTO_COPY_ENABLED = false;

    // Wait for connection to restore
    int waitCount = 0;
    while(!TerminalInfoInteger(TERMINAL_CONNECTED) && waitCount < 60) {
        Sleep(1000);
        waitCount++;
    }

    if(TerminalInfoInteger(TERMINAL_CONNECTED)) {
        AUTO_COPY_ENABLED = wasAutoCopyEnabled;
        logger.Info("Network connection restored");
    } else {
        logger.Error("Network connection timeout");
        HandleCriticalError("Network connection lost");
    }
}

//+------------------------------------------------------------------+
//| Signal validation and safety checks                             |
//+------------------------------------------------------------------+
bool ValidateSignalSafety(const SignalData &signal) {
    // Check signal age
    datetime signalTime = signal.timestamp;
    if(TimeCurrent() - signalTime > 300) { // 5 minutes old
        logger.Warning(StringFormat("Signal too old: %d seconds", TimeCurrent() - signalTime));
        return false;
    }

    // Check symbol availability
    if(!SymbolSelect(signal.symbol, true)) {
        logger.Warning(StringFormat("Symbol not available: %s", signal.symbol));
        return false;
    }

    // Check market hours
    if(!IsMarketOpen(signal.symbol)) {
        logger.Warning(StringFormat("Market closed for symbol: %s", signal.symbol));
        return false;
    }

    // Check spread
    double spread = SymbolInfoInteger(signal.symbol, SYMBOL_SPREAD) * SymbolInfoDouble(signal.symbol, SYMBOL_POINT);
    double maxSpread = SymbolInfoDouble(signal.symbol, SYMBOL_POINT) * 50; // 50 points max

    if(spread > maxSpread) {
        logger.Warning(StringFormat("Spread too high for %s: %.5f", signal.symbol, spread));
        return false;
    }

    // Check price deviation
    if(signal.action == "open" && signal.open_price > 0) {
        double currentPrice = signal.type == "buy" ?
            SymbolInfoDouble(signal.symbol, SYMBOL_ASK) :
            SymbolInfoDouble(signal.symbol, SYMBOL_BID);

        double deviation = MathAbs(currentPrice - signal.open_price) / signal.open_price * 100;

        if(deviation > 1.0) { // 1% max deviation
            logger.Warning(StringFormat("Price deviation too high: %.2f%%", deviation));
            return false;
        }
    }

    return true;
}

bool IsMarketOpen(string symbol) {
    // Check if trading is allowed
    if(!SymbolInfoInteger(symbol, SYMBOL_TRADE_MODE)) {
        return false;
    }

    // Check trading sessions
    datetime currentTime = TimeCurrent();
    MqlDateTime dt;
    TimeToStruct(currentTime, dt);

    // Skip weekends
    if(dt.day_of_week == 0 || dt.day_of_week == 6) {
        return false;
    }

    // Check for major holidays
    if(IsHoliday(currentTime)) {
        return false;
    }

    return true;
}

bool IsHoliday(datetime checkTime) {
    MqlDateTime dt;
    TimeToStruct(checkTime, dt);

    // New Year's Day
    if(dt.mon == 1 && dt.day == 1) return true;

    // Christmas
    if(dt.mon == 12 && dt.day == 25) return true;

    return false;
}

//+------------------------------------------------------------------+
//| Emergency position management                                   |
//+------------------------------------------------------------------+
void EmergencyCloseAllPositions() {
    logger.Critical("Emergency: Closing all positions");

    int closedCount = 0;

    for(int i = PositionsTotal() - 1; i >= 0; i--) {
        if(PositionSelectByIndex(i)) {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            string comment = PositionGetString(POSITION_COMMENT);

            // Only close copy trade positions
            if(StringFind(comment, "Copy") >= 0 || StringFind(comment, "copy") >= 0) {
                if(tradeExecutor != NULL) {
                    // Use trade executor if available
                    CTrade trade;
                    if(trade.PositionClose(ticket)) {
                        closedCount++;
                        logger.Info(StringFormat("Emergency closed position: %d", ticket));
                    }
                } else {
                    // Direct close if trade executor not available
                    CTrade trade;
                    if(trade.PositionClose(ticket)) {
                        closedCount++;
                    }
                }
            }
        }
    }

    logger.Info(StringFormat("Emergency closure completed. Closed %d positions", closedCount));

    // Send notification
    SendEmergencyNotification(closedCount);
}

void SendEmergencyNotification(int closedPositions) {
    string jsonData = StringFormat(
        "{"
        "\"type\":\"emergency_closure\","
        "\"follower_id\":\"%s\","
        "\"closed_positions\":%d,"
        "\"timestamp\":%d"
        "}",
        FOLLOWER_ID,
        closedPositions,
        TimeCurrent()
    );

    char postData[];
    char result[];
    string headers = "Content-Type: application/json\r\n";
    headers += "Authorization: Bearer " + API_KEY + "\r\n";
    string resultHeaders;

    StringToCharArray(jsonData, postData, 0, StringLen(jsonData));

    WebRequest("POST", API_BASE_URL + "/api/v1/followers/emergency",
               headers, 3000, postData, result, resultHeaders);
}

//+------------------------------------------------------------------+
//| Performance monitoring                                           |
//+------------------------------------------------------------------+
void MonitorPerformance() {
    static datetime lastPerformanceCheck = 0;

    if(TimeCurrent() - lastPerformanceCheck < 300) return; // Check every 5 minutes

    // Check account health
    double equity = AccountInfoDouble(ACCOUNT_EQUITY);
    double balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double margin = AccountInfoDouble(ACCOUNT_MARGIN);
    double freeMargin = AccountInfoDouble(ACCOUNT_MARGIN_FREE);

    // Check margin level
    if(margin > 0) {
        double marginLevel = equity / margin * 100;
        if(marginLevel < 200) { // Less than 200%
            logger.Warning(StringFormat("Low margin level: %.2f%%", marginLevel));

            if(marginLevel < 100) {
                logger.Critical("Margin call level reached!");
                EmergencyCloseAllPositions();
            }
        }
    }

    // Check drawdown
    if(riskManager != NULL) {
        double currentDrawdown = riskManager.GetCurrentDrawdown();
        if(currentDrawdown > MAX_DRAWDOWN * 0.8) { // 80% of max drawdown
            logger.Warning(StringFormat("High drawdown: %.2f%%", currentDrawdown));
        }
    }

    // Check daily loss
    double dailyPnL = riskManager != NULL ? riskManager.GetDailyPnL() : 0;
    if(dailyPnL < -MAX_DAILY_LOSS * 0.8) { // 80% of max daily loss
        logger.Warning(StringFormat("High daily loss: %.2f", dailyPnL));
    }

    lastPerformanceCheck = TimeCurrent();
}

//+------------------------------------------------------------------+
//| Configuration validation                                         |
//+------------------------------------------------------------------+
bool ValidateFollowerConfiguration() {
    // Check API configuration
    if(StringLen(API_BASE_URL) == 0) {
        logger.Error("API_BASE_URL not configured");
        return false;
    }

    if(StringLen(API_KEY) == 0) {
        logger.Error("API_KEY not configured");
        return false;
    }

    if(StringLen(FOLLOWER_ID) == 0) {
        logger.Error("FOLLOWER_ID not configured");
        return false;
    }

    // Check risk parameters
    if(RISK_PERCENT <= 0 || RISK_PERCENT > 100) {
        logger.Error("Invalid RISK_PERCENT");
        return false;
    }

    if(MAX_LOT_SIZE <= 0) {
        logger.Error("Invalid MAX_LOT_SIZE");
        return false;
    }

    if(MAX_DAILY_LOSS <= 0) {
        logger.Error("Invalid MAX_DAILY_LOSS");
        return false;
    }

    // Check account permissions
    if(!AccountInfoInteger(ACCOUNT_TRADE_ALLOWED)) {
        logger.Error("Trading not allowed on this account");
        return false;
    }

    if(!AccountInfoInteger(ACCOUNT_TRADE_EXPERT)) {
        logger.Error("Expert Advisor trading not allowed");
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| State management                                                 |
//+------------------------------------------------------------------+
void SaveFollowerState() {
    // Save important state variables
    GlobalVariableSet("FOLLOWER_DAILY_LOSS", dailyLoss);
    GlobalVariableSet("FOLLOWER_LAST_RESET_DATE", lastResetDate);
    GlobalVariableSet("FOLLOWER_LAST_HEARTBEAT", lastHeartbeat);
    GlobalVariableSet("FOLLOWER_AUTO_COPY_ENABLED", AUTO_COPY_ENABLED ? 1 : 0);

    logger.Info("Follower state saved");
}

void LoadFollowerState() {
    // Load state variables
    if(GlobalVariableCheck("FOLLOWER_DAILY_LOSS")) {
        dailyLoss = GlobalVariableGet("FOLLOWER_DAILY_LOSS");
    }

    if(GlobalVariableCheck("FOLLOWER_LAST_RESET_DATE")) {
        lastResetDate = (datetime)GlobalVariableGet("FOLLOWER_LAST_RESET_DATE");
    }

    if(GlobalVariableCheck("FOLLOWER_LAST_HEARTBEAT")) {
        lastHeartbeat = (datetime)GlobalVariableGet("FOLLOWER_LAST_HEARTBEAT");
    }

    if(GlobalVariableCheck("FOLLOWER_AUTO_COPY_ENABLED")) {
        AUTO_COPY_ENABLED = GlobalVariableGet("FOLLOWER_AUTO_COPY_ENABLED") > 0;
    }

    logger.Info("Follower state loaded");
}

//+------------------------------------------------------------------+
//| Graceful shutdown                                                |
//+------------------------------------------------------------------+
void GracefulShutdown() {
    logger.Info("Initiating graceful shutdown...");

    // Send offline status
    SendFollowerStatus("offline");

    // Save current state
    SaveFollowerState();

    // Wait for pending operations
    Sleep(1000);

    logger.Info("Graceful shutdown completed");
}

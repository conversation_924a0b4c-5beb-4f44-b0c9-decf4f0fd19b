# Copy Trading System

A professional, scalable copy trading platform built for MT5 with support for thousands of concurrent users.

## 🚀 Features

### Core Functionality
- **Real-time Signal Distribution**: Sub-second signal propagation from providers to followers
- **MT5 Native Integration**: Expert Advisors for seamless MT5 integration
- **Scalable Architecture**: Supports thousands of concurrent users
- **Risk Management**: Comprehensive risk controls and position sizing
- **Performance Analytics**: Detailed performance tracking and reporting

### Technical Highlights
- **Microservices Architecture**: FastAPI backend with PostgreSQL and Redis
- **Real-time Communication**: WebSocket for web clients, HTTP polling for MT5 EAs
- **High Availability**: Load balancing, auto-scaling, and failover support
- **Security**: JWT authentication, API rate limiting, and data encryption
- **Monitoring**: Prometheus metrics, Grafana dashboards, and ELK logging

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Provider EA   │───▶│   Backend API   │◀───│  Follower EA    │
│   (MT5 Signal   │    │   (FastAPI)     │    │  (MT5 Execute)  │
│   Generator)    │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │  Admin Dashboard│
                    │   (React SPA)   │
                    └─────────────────┘
```

### Data Flow
1. **Provider EA** captures trades and sends signals via HTTP API
2. **Backend** processes, validates, and distributes signals
3. **Follower EA** receives signals via HTTP polling and executes trades
4. **Dashboard** provides real-time monitoring and management

## 📋 Prerequisites

### Development Environment
- **Python 3.11+** for backend development
- **Node.js 18+** for frontend development
- **PostgreSQL 15+** for primary database
- **Redis 7+** for caching and real-time data
- **Docker & Docker Compose** for containerization
- **MetaTrader 5** for EA development and testing

### Production Environment
- **Linux server** (Ubuntu 22.04 LTS recommended)
- **4+ CPU cores, 8GB+ RAM** for moderate load
- **SSD storage** for database performance
- **SSL certificate** for HTTPS/WSS connections

## 🚀 Quick Start

### 1. Clone Repository
```bash
git clone https://github.com/your-org/copy-trading-system.git
cd copy-trading-system
```

### 2. Environment Setup
```bash
# Copy environment file
cp .env.example .env

# Edit configuration
nano .env
```

### 3. Start Development Environment
```bash
# Start all services
docker-compose up -d

# Check service status
docker-compose ps
```

### 4. Initialize Database
```bash
# Run migrations
docker-compose exec backend alembic upgrade head

# Create admin user
docker-compose exec backend python scripts/create_admin.py
```

### 5. Access Services
- **API Documentation**: http://localhost:8000/docs
- **Admin Dashboard**: http://localhost:3000
- **Grafana Monitoring**: http://localhost:3001
- **Database**: localhost:5432

## 🔧 Configuration

### Backend Configuration
Key settings in `.env`:

```env
# Database
DATABASE_URL=postgresql://user:pass@localhost:5432/copytrading_db

# Redis
REDIS_URL=redis://localhost:6379/0

# Security
SECRET_KEY=your-secret-key-min-32-chars
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Risk Management
MAX_RISK_PER_TRADE=5.0
MAX_DAILY_LOSS=1000.0
MAX_DRAWDOWN=20.0
```

### EA Configuration
Edit `ea/config/ea_config.ini`:

```ini
[API]
base_url=https://api.copytrading.com
api_key=your-api-key

[PROVIDER]
provider_id=your-provider-id
auto_send_signals=true
max_daily_signals=50

[FOLLOWER]
follower_id=your-follower-id
auto_copy_enabled=true
copy_ratio=1.0
risk_percent=2.0
```

## 📊 Monitoring & Analytics

### System Metrics
- **API Performance**: Response times, error rates, throughput
- **Database**: Connection pool, query performance, storage
- **Redis**: Memory usage, hit rates, connection count
- **WebSocket**: Active connections, message rates

### Business Metrics
- **Signal Distribution**: Latency, success rates, volume
- **Trade Execution**: Fill rates, slippage, P&L
- **User Activity**: Active providers/followers, subscription rates

### Dashboards
- **System Health**: Infrastructure monitoring
- **Trading Performance**: P&L, drawdown, win rates
- **User Analytics**: Activity, growth, retention

## 🧪 Testing

### Backend Tests
```bash
# Run all tests
docker-compose exec backend pytest

# Run with coverage
docker-compose exec backend pytest --cov=api

# Run specific test file
docker-compose exec backend pytest tests/test_signals.py -v
```

### EA Tests
```bash
# Compile test script
# In MetaEditor: Compile ea/tests/test_ea_functionality.mq5

# Run in MT5 Strategy Tester
# 1. Open MT5 Strategy Tester
# 2. Select test_ea_functionality script
# 3. Configure test parameters
# 4. Run test
```

### Load Testing
```bash
# API load testing
docker-compose exec backend locust -f tests/load_test.py

# WebSocket load testing
node tests/websocket_load_test.js
```

## 🚀 Deployment

### Production Deployment
```bash
# Build production images
docker-compose -f docker-compose.prod.yml build

# Deploy with environment variables
export DATABASE_URL="postgresql://..."
export REDIS_URL="redis://..."
export SECRET_KEY="production-secret-key"

# Start production services
docker-compose -f docker-compose.prod.yml up -d

# Run database migrations
docker-compose -f docker-compose.prod.yml exec backend alembic upgrade head
```

### Scaling
```bash
# Scale backend services
docker-compose -f docker-compose.prod.yml up -d --scale backend=3

# Scale with Docker Swarm
docker stack deploy -c docker-compose.prod.yml copytrading
```

## 📚 Documentation

### Complete Documentation Suite
- **[Architecture Guide](docs/architecture.md)** - System architecture and design principles
- **[API Reference](docs/api-reference.md)** - Complete API documentation with examples
- **[EA Development Guide](docs/ea-development.md)** - Expert Advisor development and deployment
- **[Deployment Guide](docs/deployment.md)** - Production deployment and maintenance
- **[User Manual](docs/user-manual.md)** - End-user guide for providers and followers
- **[Product Roadmap](docs/roadmap.md)** - Future development plans and milestones

### Quick API Examples

#### Authentication
```bash
# Login
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}'
```

#### Signal Management
```bash
# Create signal (Provider EA)
curl -X POST "http://localhost:8000/api/v1/signals/" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "provider_id": "provider_123",
    "action": "open",
    "symbol": "EURUSD",
    "type": "buy",
    "volume": 0.1,
    "open_price": 1.1234,
    "stop_loss": 1.1200,
    "take_profit": 1.1300
  }'

# Poll for signals (Follower EA)
curl -H "Authorization: Bearer YOUR_TOKEN" \
  "http://localhost:8000/api/v1/mt5/poll"
```

#### Partial Close Support
```bash
# Partial close signal
curl -X POST "http://localhost:8000/api/v1/signals/" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "provider_id": "provider_123",
    "action": "close",
    "symbol": "EURUSD",
    "original_ticket": 12345,
    "partial_close_volume": 0.05,
    "remaining_volume": 0.05,
    "is_partial_close": true
  }'
```

## 🔒 Security

### Authentication & Authorization
- **JWT Tokens**: Secure API access with configurable expiration
- **API Keys**: Long-lived tokens for EA authentication
- **Role-based Access**: Provider, Follower, and Admin roles
- **Rate Limiting**: Prevent API abuse and DoS attacks

### Data Protection
- **Encryption**: All sensitive data encrypted at rest and in transit
- **Input Validation**: Comprehensive request validation and sanitization
- **SQL Injection Prevention**: Parameterized queries and ORM usage
- **CORS Protection**: Configurable cross-origin request policies

### Infrastructure Security
- **SSL/TLS**: HTTPS/WSS for all external communications
- **Firewall Rules**: Restricted network access to services
- **Container Security**: Regular image scanning and updates
- **Backup Encryption**: Encrypted database and file backups

## 🐛 Troubleshooting

### Common Issues

#### EA Connection Problems
```
Error: "Failed to connect to API"
Solution: Check API_BASE_URL and network connectivity
```

#### Database Connection Issues
```
Error: "Database connection failed"
Solution: Verify DATABASE_URL and PostgreSQL service status
```

#### WebSocket Connection Drops
```
Error: "WebSocket disconnected"
Solution: Check network stability and firewall settings
```

### Debug Mode
```bash
# Enable debug logging
export LOG_LEVEL=DEBUG
docker-compose restart backend

# View logs
docker-compose logs -f backend
```

### Health Checks
```bash
# Check API health
curl http://localhost:8000/health

# Check detailed health
curl http://localhost:8000/health/detailed

# Check database
docker-compose exec postgres pg_isready

# Check Redis
docker-compose exec redis redis-cli ping
```

## 📈 Performance Optimization

### Database Optimization
- **Indexing**: Proper indexes on frequently queried columns
- **Connection Pooling**: Optimized connection pool settings
- **Query Optimization**: Efficient queries and pagination
- **Partitioning**: Table partitioning for large datasets

### Caching Strategy
- **Redis Caching**: Frequently accessed data cached in Redis
- **Application Caching**: In-memory caching for static data
- **CDN**: Static assets served via CDN
- **Database Query Caching**: Query result caching

### Scaling Strategies
- **Horizontal Scaling**: Multiple backend instances with load balancing
- **Database Replication**: Read replicas for query distribution
- **Microservices**: Service decomposition for independent scaling
- **Auto-scaling**: Kubernetes HPA for dynamic scaling

## 🤝 Contributing

### Development Workflow
1. Fork the repository
2. Create feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open Pull Request

### Code Standards
- **Python**: Follow PEP 8, use Black formatter
- **TypeScript**: Follow ESLint rules, use Prettier
- **MQL5**: Follow MetaQuotes coding standards
- **Documentation**: Update docs for all changes

### Testing Requirements
- **Unit Tests**: Minimum 80% code coverage
- **Integration Tests**: Test API endpoints and workflows
- **EA Tests**: Test all EA functionality
- **Performance Tests**: Ensure no performance regressions

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

### Documentation
- **API Docs**: http://localhost:8000/docs
- **Architecture Guide**: [docs/architecture.md](docs/architecture.md)
- **Deployment Guide**: [docs/deployment.md](docs/deployment.md)
- **EA Development**: [docs/ea-development.md](docs/ea-development.md)

### Community
- **Issues**: GitHub Issues for bug reports
- **Discussions**: GitHub Discussions for questions
- **Discord**: Join our Discord server for real-time support

### Commercial Support
For enterprise support, custom development, or consulting services, contact: <EMAIL>

---

**Built with ❤️ for the trading community**

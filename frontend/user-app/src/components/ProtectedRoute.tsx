import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Box, CircularProgress, Typography } from '@mui/material';
import { useAuthStore } from '../stores/authStore';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: 'provider' | 'follower' | 'both';
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  requiredRole 
}) => {
  const { isAuthenticated, user, isLoading, refreshUser } = useAuthStore();
  const location = useLocation();

  useEffect(() => {
    // If authenticated but no user data, try to refresh
    if (isAuthenticated && !user && !isLoading) {
      refreshUser();
    }
  }, [isAuthenticated, user, isLoading, refreshUser]);

  // Show loading spinner while checking authentication
  if (isLoading || (isAuthenticated && !user)) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100vh',
          gap: 2,
        }}
      >
        <CircularProgress size={40} />
        <Typography variant="body2" color="text.secondary">
          Loading...
        </Typography>
      </Box>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated || !user) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Check role requirements
  if (requiredRole) {
    const hasRequiredRole = 
      user.user_type === requiredRole || 
      user.user_type === 'both';

    if (!hasRequiredRole) {
      // Redirect to appropriate dashboard based on user type
      const redirectPath = user.user_type === 'provider' 
        ? '/provider' 
        : user.user_type === 'follower'
        ? '/follower'
        : '/dashboard';
      
      return <Navigate to={redirectPath} replace />;
    }
  }

  return <>{children}</>;
};

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

// Create axios instance
export const apiClient: AxiosInstance = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8000',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
apiClient.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // Add timestamp to prevent caching
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now(),
      };
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error) => {
    // Handle common errors
    if (error.response?.status === 401) {
      // Unauthorized - redirect to login
      const currentPath = window.location.pathname;
      if (currentPath !== '/login' && currentPath !== '/register') {
        window.location.href = '/login';
      }
    }

    return Promise.reject(error);
  }
);

// API service functions
export const apiService = {
  // Auth endpoints
  auth: {
    login: (email: string, password: string) =>
      apiClient.post('/api/v1/auth/login', { email, password }),
    
    register: (userData: any) =>
      apiClient.post('/api/v1/auth/register', userData),
    
    me: () =>
      apiClient.get('/api/v1/auth/me'),
    
    refreshToken: () =>
      apiClient.post('/api/v1/auth/refresh'),
    
    logout: () =>
      apiClient.post('/api/v1/auth/logout'),
  },

  // Provider endpoints
  providers: {
    list: (params?: any) =>
      apiClient.get('/api/v1/providers/', { params }),
    
    get: (providerId: string) =>
      apiClient.get(`/api/v1/providers/${providerId}`),
    
    update: (providerId: string, data: any) =>
      apiClient.put(`/api/v1/providers/${providerId}`, data),
    
    performance: (providerId: string, params?: any) =>
      apiClient.get(`/api/v1/providers/${providerId}/performance`, { params }),
    
    followers: (providerId: string) =>
      apiClient.get(`/api/v1/providers/${providerId}/followers`),
  },

  // Follower endpoints
  followers: {
    list: (params?: any) =>
      apiClient.get('/api/v1/followers/', { params }),
    
    get: (followerId: string) =>
      apiClient.get(`/api/v1/followers/${followerId}`),
    
    update: (followerId: string, data: any) =>
      apiClient.put(`/api/v1/followers/${followerId}`, data),
    
    subscriptions: (followerId: string) =>
      apiClient.get(`/api/v1/followers/${followerId}/subscriptions`),
    
    subscribe: (followerId: string, data: any) =>
      apiClient.post(`/api/v1/followers/${followerId}/subscriptions`, data),
    
    updateSubscription: (followerId: string, subscriptionId: string, data: any) =>
      apiClient.put(`/api/v1/followers/${followerId}/subscriptions/${subscriptionId}`, data),
  },

  // Signal endpoints
  signals: {
    list: (params?: any) =>
      apiClient.get('/api/v1/signals/', { params }),
    
    get: (signalId: string) =>
      apiClient.get(`/api/v1/signals/${signalId}`),
    
    stats: () =>
      apiClient.get('/api/v1/signals/stats/summary'),
  },

  // Dashboard endpoints
  dashboard: {
    overview: () =>
      apiClient.get('/api/v1/dashboard/overview'),
    
    performance: (params?: any) =>
      apiClient.get('/api/v1/dashboard/performance', { params }),
    
    recentActivity: () =>
      apiClient.get('/api/v1/dashboard/recent-activity'),
  },

  // Account endpoints
  account: {
    settings: () =>
      apiClient.get('/api/v1/account/settings'),
    
    updateSettings: (data: any) =>
      apiClient.put('/api/v1/account/settings', data),
    
    changePassword: (data: any) =>
      apiClient.post('/api/v1/account/change-password', data),
    
    apiKeys: () =>
      apiClient.get('/api/v1/auth/api-keys'),
    
    createApiKey: (data: any) =>
      apiClient.post('/api/v1/auth/api-keys', data),
    
    deleteApiKey: (keyId: string) =>
      apiClient.delete(`/api/v1/auth/api-keys/${keyId}`),
  },

  // Health endpoints
  health: {
    basic: () =>
      apiClient.get('/health'),
    
    detailed: () =>
      apiClient.get('/health/detailed'),
  },
};

// Utility functions
export const handleApiError = (error: any): string => {
  if (error.response?.data?.detail) {
    return error.response.data.detail;
  }
  
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  
  if (error.message) {
    return error.message;
  }
  
  return 'An unexpected error occurred';
};

export const isApiError = (error: any): boolean => {
  return error.response !== undefined;
};

export default apiClient;

import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Box } from '@mui/material';
import { useAuthStore } from './stores/authStore';
import { ProtectedRoute } from './components/ProtectedRoute';
import { Layout } from './components/Layout';

// Auth pages
import { LoginPage } from './pages/auth/LoginPage';
import { RegisterPage } from './pages/auth/RegisterPage';
// import { ForgotPasswordPage } from './pages/auth/ForgotPasswordPage';

// Dashboard pages
import { DashboardPage } from './pages/dashboard/DashboardPage';
import { ProviderDashboard } from './pages/provider/ProviderDashboard';
import { FollowerDashboard } from './pages/follower/FollowerDashboard';

// Import placeholder pages
import {
  ProviderProfile,
  ProviderSignals,
  ProviderPerformance,
  ProviderSettings,
  FollowerDashboard,
  FollowerProfile,
  FollowerSubscriptions,
  FollowerPerformance,
  FollowerSettings,
  ProvidersMarketplace,
  ProviderDetails,
  AccountSettings,
  BillingPage,
  SecurityPage,
  LandingPage,
  AboutPage,
  PricingPage,
  ContactPage,
  NotFoundPage,
} from './pages/placeholder';
import { ErrorBoundary } from './components/ErrorBoundary';

function App() {
  const { isAuthenticated, user } = useAuthStore();

  return (
    <ErrorBoundary>
      <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
        <Routes>
          {/* Public routes */}
          <Route path="/" element={<LandingPage />} />
          <Route path="/about" element={<AboutPage />} />
          <Route path="/pricing" element={<PricingPage />} />
          <Route path="/contact" element={<ContactPage />} />
          
          {/* Auth routes */}
          <Route 
            path="/login" 
            element={
              isAuthenticated ? (
                <Navigate to="/dashboard" replace />
              ) : (
                <LoginPage />
              )
            } 
          />
          <Route 
            path="/register" 
            element={
              isAuthenticated ? (
                <Navigate to="/dashboard" replace />
              ) : (
                <RegisterPage />
              )
            } 
          />
          {/* <Route path="/forgot-password" element={<ForgotPasswordPage />} /> */}
          
          {/* Protected routes */}
          <Route
            path="/dashboard"
            element={
              <ProtectedRoute>
                <Layout>
                  <DashboardPage />
                </Layout>
              </ProtectedRoute>
            }
          />
          
          {/* Provider routes */}
          <Route
            path="/provider/*"
            element={
              <ProtectedRoute requiredRole="provider">
                <Layout>
                  <Routes>
                    <Route index element={<ProviderDashboard />} />
                    <Route path="profile" element={<ProviderProfile />} />
                    <Route path="signals" element={<ProviderSignals />} />
                    <Route path="performance" element={<ProviderPerformance />} />
                    <Route path="settings" element={<ProviderSettings />} />
                  </Routes>
                </Layout>
              </ProtectedRoute>
            }
          />
          
          {/* Follower routes */}
          <Route
            path="/follower/*"
            element={
              <ProtectedRoute requiredRole="follower">
                <Layout>
                  <Routes>
                    <Route index element={<FollowerDashboard />} />
                    <Route path="profile" element={<FollowerProfile />} />
                    <Route path="subscriptions" element={<FollowerSubscriptions />} />
                    <Route path="performance" element={<FollowerPerformance />} />
                    <Route path="settings" element={<FollowerSettings />} />
                  </Routes>
                </Layout>
              </ProtectedRoute>
            }
          />
          
          {/* Marketplace routes */}
          <Route
            path="/marketplace/*"
            element={
              <ProtectedRoute>
                <Layout>
                  <Routes>
                    <Route index element={<ProvidersMarketplace />} />
                    <Route path="provider/:providerId" element={<ProviderDetails />} />
                  </Routes>
                </Layout>
              </ProtectedRoute>
            }
          />
          
          {/* Account routes */}
          <Route
            path="/account/*"
            element={
              <ProtectedRoute>
                <Layout>
                  <Routes>
                    <Route path="settings" element={<AccountSettings />} />
                    <Route path="billing" element={<BillingPage />} />
                    <Route path="security" element={<SecurityPage />} />
                  </Routes>
                </Layout>
              </ProtectedRoute>
            }
          />
          
          {/* Redirect based on user type */}
          <Route
            path="/app"
            element={
              <ProtectedRoute>
                <Navigate 
                  to={
                    user?.user_type === 'provider' 
                      ? '/provider' 
                      : user?.user_type === 'follower'
                      ? '/follower'
                      : '/dashboard'
                  } 
                  replace 
                />
              </ProtectedRoute>
            }
          />
          
          {/* 404 page */}
          <Route path="*" element={<NotFoundPage />} />
        </Routes>
      </Box>
    </ErrorBoundary>
  );
}

export default App;

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { apiClient } from '../services/apiClient';

export interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  user_type: 'provider' | 'follower' | 'both';
  is_active: boolean;
  is_verified: boolean;
  created_at: string;
  provider_id?: string;
  follower_id?: string;
}

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

interface AuthActions {
  login: (email: string, password: string) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => void;
  refreshUser: () => Promise<void>;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

interface RegisterData {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  user_type: 'provider' | 'follower';
}

type AuthStore = AuthState & AuthActions;

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      login: async (email: string, password: string) => {
        try {
          set({ isLoading: true, error: null });

          const response = await apiClient.post('/api/v1/auth/login', {
            email,
            password,
          });

          const { access_token, user } = response.data;

          // Set token in API client
          apiClient.defaults.headers.common['Authorization'] = `Bearer ${access_token}`;

          set({
            user,
            token: access_token,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error: any) {
          const errorMessage = error.response?.data?.detail || 'Login failed';
          set({
            error: errorMessage,
            isLoading: false,
            isAuthenticated: false,
            user: null,
            token: null,
          });
          throw new Error(errorMessage);
        }
      },

      register: async (userData: RegisterData) => {
        try {
          set({ isLoading: true, error: null });

          const response = await apiClient.post('/api/v1/auth/register', userData);

          const { access_token, user } = response.data;

          // Set token in API client
          apiClient.defaults.headers.common['Authorization'] = `Bearer ${access_token}`;

          set({
            user,
            token: access_token,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error: any) {
          const errorMessage = error.response?.data?.detail || 'Registration failed';
          set({
            error: errorMessage,
            isLoading: false,
            isAuthenticated: false,
            user: null,
            token: null,
          });
          throw new Error(errorMessage);
        }
      },

      logout: () => {
        // Clear token from API client
        delete apiClient.defaults.headers.common['Authorization'];

        set({
          user: null,
          token: null,
          isAuthenticated: false,
          error: null,
        });
      },

      refreshUser: async () => {
        try {
          const { token } = get();
          
          if (!token) {
            throw new Error('No token available');
          }

          // Set token in API client if not already set
          if (!apiClient.defaults.headers.common['Authorization']) {
            apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
          }

          const response = await apiClient.get('/api/v1/auth/me');
          const user = response.data;

          set({ user, error: null });
        } catch (error: any) {
          console.error('Failed to refresh user:', error);
          
          // If token is invalid, logout
          if (error.response?.status === 401) {
            get().logout();
          }
          
          set({ error: 'Failed to refresh user data' });
        }
      },

      clearError: () => {
        set({ error: null });
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
      onRehydrateStorage: () => (state) => {
        // Set token in API client when rehydrating from storage
        if (state?.token) {
          apiClient.defaults.headers.common['Authorization'] = `Bearer ${state.token}`;
        }
      },
    }
  )
);

// Initialize auth on app start
export const initializeAuth = () => {
  const { token, refreshUser } = useAuthStore.getState();
  
  if (token) {
    // Set token in API client
    apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    
    // Refresh user data
    refreshUser().catch(() => {
      // If refresh fails, logout
      useAuthStore.getState().logout();
    });
  }
};

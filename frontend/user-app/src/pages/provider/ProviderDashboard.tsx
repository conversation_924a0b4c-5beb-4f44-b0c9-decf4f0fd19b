import React from 'react';
import {
  <PERSON>,
  <PERSON>rid,
  Card,
  CardContent,
  <PERSON><PERSON>graphy,
  Button,
  Avatar,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from '@mui/material';
import {
  TrendingUp,
  People,
  Signal,
  AttachMoney,
  Add,
  Visibility,
} from '@mui/icons-material';

export const ProviderDashboard: React.FC = () => {
  const stats = [
    {
      title: 'Total Followers',
      value: '156',
      change: '+12 this week',
      color: 'primary',
      icon: <People />,
    },
    {
      title: 'Active Signals',
      value: '8',
      change: '3 pending',
      color: 'success',
      icon: <Signal />,
    },
    {
      title: 'Monthly Earnings',
      value: '$2,450',
      change: '+15.2%',
      color: 'success',
      icon: <AttachMoney />,
    },
    {
      title: 'Win Rate',
      value: '78.5%',
      change: '****%',
      color: 'success',
      icon: <TrendingUp />,
    },
  ];

  const recentSignals = [
    {
      id: 'SIG001',
      symbol: 'EURUSD',
      type: 'Buy',
      volume: '0.1',
      entry: '1.0850',
      status: 'Active',
      profit: '+$125.50',
      followers: 45,
    },
    {
      id: 'SIG002',
      symbol: 'GBPUSD',
      type: 'Sell',
      volume: '0.2',
      entry: '1.2650',
      status: 'Closed',
      profit: '+$89.20',
      followers: 38,
    },
    {
      id: 'SIG003',
      symbol: 'USDJPY',
      type: 'Buy',
      volume: '0.15',
      entry: '149.50',
      status: 'Active',
      profit: '-$45.30',
      followers: 52,
    },
  ];

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" gutterBottom fontWeight={700}>
            Provider Dashboard
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage your trading signals and track your performance
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<Add />}
          size="large"
        >
          Create Signal
        </Button>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {stats.map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar
                    sx={{
                      backgroundColor: `${stat.color}.main`,
                      color: 'white',
                      mr: 2,
                    }}
                  >
                    {stat.icon}
                  </Avatar>
                  <Box>
                    <Typography variant="h5" fontWeight={700}>
                      {stat.value}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {stat.title}
                    </Typography>
                  </Box>
                </Box>
                <Typography variant="body2" color="success.main">
                  {stat.change}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Grid container spacing={3}>
        {/* Recent Signals */}
        <Grid item xs={12} lg={8}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h6" fontWeight={600}>
                  Recent Signals
                </Typography>
                <Button endIcon={<Visibility />} size="small">
                  View All
                </Button>
              </Box>

              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Signal ID</TableCell>
                      <TableCell>Symbol</TableCell>
                      <TableCell>Type</TableCell>
                      <TableCell>Volume</TableCell>
                      <TableCell>Entry</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>P&L</TableCell>
                      <TableCell>Followers</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {recentSignals.map((signal) => (
                      <TableRow key={signal.id}>
                        <TableCell>{signal.id}</TableCell>
                        <TableCell fontWeight={600}>{signal.symbol}</TableCell>
                        <TableCell>
                          <Chip
                            label={signal.type}
                            color={signal.type === 'Buy' ? 'success' : 'error'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>{signal.volume}</TableCell>
                        <TableCell>{signal.entry}</TableCell>
                        <TableCell>
                          <Chip
                            label={signal.status}
                            color={signal.status === 'Active' ? 'primary' : 'default'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Typography
                            color={signal.profit.startsWith('+') ? 'success.main' : 'error.main'}
                            fontWeight={600}
                          >
                            {signal.profit}
                          </Typography>
                        </TableCell>
                        <TableCell>{signal.followers}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Quick Actions */}
        <Grid item xs={12} lg={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" fontWeight={600} gutterBottom>
                Quick Actions
              </Typography>

              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Button
                  fullWidth
                  variant="contained"
                  startIcon={<Add />}
                >
                  Create New Signal
                </Button>

                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<TrendingUp />}
                >
                  View Performance
                </Button>

                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<People />}
                >
                  Manage Followers
                </Button>

                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<Signal />}
                >
                  Signal History
                </Button>
              </Box>

              <Box sx={{ mt: 3, p: 2, backgroundColor: 'grey.50', borderRadius: 1 }}>
                <Typography variant="body2" fontWeight={600} gutterBottom>
                  Performance Summary
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total Signals: 156
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Successful: 122 (78.5%)
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total Profit: $12,450
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

import React from 'react';
import { Box, Typography, Card, CardContent, Button } from '@mui/material';
import { Construction } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

interface PlaceholderPageProps {
  title: string;
  description?: string;
}

export const PlaceholderPage: React.FC<PlaceholderPageProps> = ({ title, description }) => {
  const navigate = useNavigate();

  return (
    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '60vh' }}>
      <Card sx={{ maxWidth: 400, textAlign: 'center' }}>
        <CardContent sx={{ py: 4 }}>
          <Construction sx={{ fontSize: 64, color: 'primary.main', mb: 2 }} />
          <Typography variant="h5" gutterBottom fontWeight={600}>
            {title}
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
            {description || 'This page is under construction. Please check back later.'}
          </Typography>
          <Button variant="contained" onClick={() => navigate('/dashboard')}>
            Back to Dashboard
          </Button>
        </CardContent>
      </Card>
    </Box>
  );
};

// Individual placeholder components
export const ProviderProfile = () => (
  <PlaceholderPage title="Provider Profile" description="Manage your provider profile and settings." />
);

export const ProviderSignals = () => (
  <PlaceholderPage title="Provider Signals" description="View and manage your trading signals." />
);

export const ProviderPerformance = () => (
  <PlaceholderPage title="Provider Performance" description="Analyze your trading performance and statistics." />
);

export const ProviderSettings = () => (
  <PlaceholderPage title="Provider Settings" description="Configure your provider account settings." />
);

export const FollowerDashboard = () => (
  <PlaceholderPage title="Follower Dashboard" description="Monitor your followed providers and performance." />
);

export const FollowerProfile = () => (
  <PlaceholderPage title="Follower Profile" description="Manage your follower profile and preferences." />
);

export const FollowerSubscriptions = () => (
  <PlaceholderPage title="Subscriptions" description="Manage your provider subscriptions and copy settings." />
);

export const FollowerPerformance = () => (
  <PlaceholderPage title="Follower Performance" description="Track your copy trading performance and results." />
);

export const FollowerSettings = () => (
  <PlaceholderPage title="Follower Settings" description="Configure your follower account settings." />
);

export const ProvidersMarketplace = () => (
  <PlaceholderPage title="Providers Marketplace" description="Browse and discover top trading providers." />
);

export const ProviderDetails = () => (
  <PlaceholderPage title="Provider Details" description="View detailed information about a specific provider." />
);

export const AccountSettings = () => (
  <PlaceholderPage title="Account Settings" description="Manage your account information and preferences." />
);

export const BillingPage = () => (
  <PlaceholderPage title="Billing" description="Manage your subscription and payment information." />
);

export const SecurityPage = () => (
  <PlaceholderPage title="Security" description="Manage your account security settings and API keys." />
);

export const LandingPage = () => (
  <PlaceholderPage title="Welcome to CopyTrading Pro" description="Professional copy trading platform for MT5." />
);

export const AboutPage = () => (
  <PlaceholderPage title="About Us" description="Learn more about CopyTrading Pro and our mission." />
);

export const PricingPage = () => (
  <PlaceholderPage title="Pricing" description="Choose the perfect plan for your trading needs." />
);

export const ContactPage = () => (
  <PlaceholderPage title="Contact Us" description="Get in touch with our support team." />
);

export const NotFoundPage = () => (
  <PlaceholderPage title="Page Not Found" description="The page you're looking for doesn't exist." />
);

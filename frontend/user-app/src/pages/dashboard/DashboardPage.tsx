import React from 'react';
import {
  <PERSON>,
  <PERSON>rid,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  Avatar,
  Chip,
  LinearProgress,
} from '@mui/material';
import {
  TrendingUp,
  People,
  AccountBalance,
  Notifications,
  Add,
  ArrowForward,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../../stores/authStore';

export const DashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuthStore();

  const stats = [
    {
      title: 'Total Balance',
      value: '$12,450.00',
      change: '+5.2%',
      color: 'success',
      icon: <AccountBalance />,
    },
    {
      title: 'Active Signals',
      value: '8',
      change: '+2',
      color: 'primary',
      icon: <TrendingUp />,
    },
    {
      title: 'Followers',
      value: '156',
      change: '+12',
      color: 'info',
      icon: <People />,
    },
    {
      title: 'Win Rate',
      value: '78.5%',
      change: '+1.2%',
      color: 'success',
      icon: <TrendingUp />,
    },
  ];

  const recentActivity = [
    {
      type: 'signal',
      title: 'New signal: EURUSD Buy',
      time: '2 minutes ago',
      status: 'success',
    },
    {
      type: 'follower',
      title: 'New follower: John Doe',
      time: '15 minutes ago',
      status: 'info',
    },
    {
      type: 'trade',
      title: 'Trade closed: +$125.50',
      time: '1 hour ago',
      status: 'success',
    },
    {
      type: 'signal',
      title: 'Signal modified: GBPUSD',
      time: '2 hours ago',
      status: 'warning',
    },
  ];

  return (
    <Box>
      {/* Welcome Section */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom fontWeight={700}>
          Welcome back, {user?.first_name}! 👋
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Here's what's happening with your trading today.
        </Typography>
      </Box>

      {/* Quick Actions */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {user?.user_type === 'provider' || user?.user_type === 'both' ? (
          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant="contained"
              size="large"
              startIcon={<Add />}
              onClick={() => navigate('/provider')}
              sx={{ py: 2 }}
            >
              Provider Dashboard
            </Button>
          </Grid>
        ) : null}
        
        {user?.user_type === 'follower' || user?.user_type === 'both' ? (
          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant="outlined"
              size="large"
              startIcon={<People />}
              onClick={() => navigate('/follower')}
              sx={{ py: 2 }}
            >
              Follower Dashboard
            </Button>
          </Grid>
        ) : null}

        <Grid item xs={12} sm={6} md={3}>
          <Button
            fullWidth
            variant="outlined"
            size="large"
            startIcon={<TrendingUp />}
            onClick={() => navigate('/marketplace')}
            sx={{ py: 2 }}
          >
            Browse Providers
          </Button>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Button
            fullWidth
            variant="outlined"
            size="large"
            startIcon={<Notifications />}
            onClick={() => navigate('/account/settings')}
            sx={{ py: 2 }}
          >
            Account Settings
          </Button>
        </Grid>
      </Grid>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {stats.map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar
                    sx={{
                      backgroundColor: `${stat.color}.main`,
                      color: 'white',
                      mr: 2,
                    }}
                  >
                    {stat.icon}
                  </Avatar>
                  <Box>
                    <Typography variant="h6" fontWeight={600}>
                      {stat.value}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {stat.title}
                    </Typography>
                  </Box>
                </Box>
                <Chip
                  label={stat.change}
                  color={stat.color as any}
                  size="small"
                  variant="outlined"
                />
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Grid container spacing={3}>
        {/* Recent Activity */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h6" fontWeight={600}>
                  Recent Activity
                </Typography>
                <Button endIcon={<ArrowForward />} size="small">
                  View All
                </Button>
              </Box>

              <Box>
                {recentActivity.map((activity, index) => (
                  <Box
                    key={index}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      py: 2,
                      borderBottom: index < recentActivity.length - 1 ? '1px solid' : 'none',
                      borderColor: 'divider',
                    }}
                  >
                    <Avatar
                      sx={{
                        backgroundColor: `${activity.status}.main`,
                        color: 'white',
                        mr: 2,
                        width: 32,
                        height: 32,
                      }}
                    >
                      {activity.type === 'signal' ? <TrendingUp /> : 
                       activity.type === 'follower' ? <People /> : 
                       <AccountBalance />}
                    </Avatar>
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="body2" fontWeight={500}>
                        {activity.title}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {activity.time}
                      </Typography>
                    </Box>
                  </Box>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Performance Overview */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" fontWeight={600} gutterBottom>
                Performance Overview
              </Typography>

              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Monthly Goal</Typography>
                  <Typography variant="body2" fontWeight={600}>78%</Typography>
                </Box>
                <LinearProgress variant="determinate" value={78} sx={{ height: 8, borderRadius: 4 }} />
              </Box>

              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Risk Level</Typography>
                  <Typography variant="body2" fontWeight={600}>Medium</Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={60} 
                  color="warning"
                  sx={{ height: 8, borderRadius: 4 }} 
                />
              </Box>

              <Box>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Total Profit This Month
                </Typography>
                <Typography variant="h5" color="success.main" fontWeight={700}>
                  +$2,450.00
                </Typography>
              </Box>

              <Button
                fullWidth
                variant="outlined"
                sx={{ mt: 2 }}
                onClick={() => navigate('/account/settings')}
              >
                View Detailed Report
              </Button>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

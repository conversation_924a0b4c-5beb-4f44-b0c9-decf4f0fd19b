import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

// Types
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  user: {
    id: string;
    email: string;
    first_name?: string;
    last_name?: string;
    user_type: string;
    is_verified: boolean;
  };
}

// API Configuration
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

class ApiClient {
  private client: AxiosInstance;
  private token: string | null = null;

  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Load token from localStorage
    this.token = localStorage.getItem('access_token');
    if (this.token) {
      this.setAuthToken(this.token);
    }

    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        // Add timestamp to prevent caching
        if (config.method === 'get') {
          config.params = {
            ...config.params,
            _t: Date.now(),
          };
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      (error) => {
        if (error.response?.status === 401) {
          this.clearAuth();
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Authentication methods
  setAuthToken(token: string) {
    this.token = token;
    this.client.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    localStorage.setItem('access_token', token);
  }

  clearAuth() {
    this.token = null;
    delete this.client.defaults.headers.common['Authorization'];
    localStorage.removeItem('access_token');
  }

  // HTTP methods
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.get<T>(url, config);
    return response.data;
  }

  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.post<T>(url, data, config);
    return response.data;
  }

  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.put<T>(url, data, config);
    return response.data;
  }

  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.delete<T>(url, config);
    return response.data;
  }

  // Authentication API
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    const response = await this.post<LoginResponse>('/api/v1/auth/login', credentials);
    if (response.access_token) {
      this.setAuthToken(response.access_token);
    }
    return response;
  }

  async logout(): Promise<void> {
    try {
      await this.post('/api/v1/auth/logout');
    } finally {
      this.clearAuth();
    }
  }

  async getCurrentUser() {
    return this.get('/api/v1/auth/me');
  }

  // Dashboard API
  async getDashboardStats() {
    return this.get('/api/v1/stats');
  }

  async getSystemHealth() {
    return this.get('/health/detailed');
  }

  // Providers API
  async getProviders(params?: {
    page?: number;
    limit?: number;
    is_verified?: boolean;
    is_active?: boolean;
  }) {
    return this.get<PaginatedResponse<any>>('/api/v1/providers', { params });
  }

  async getProvider(providerId: string) {
    return this.get(`/api/v1/providers/${providerId}`);
  }

  async updateProvider(providerId: string, data: any) {
    return this.put(`/api/v1/providers/${providerId}`, data);
  }

  async getProviderPerformance(providerId: string, days: number = 30) {
    return this.get(`/api/v1/providers/${providerId}/performance`, {
      params: { days }
    });
  }

  // Followers API
  async getFollowers(params?: {
    page?: number;
    limit?: number;
    is_active?: boolean;
  }) {
    return this.get<PaginatedResponse<any>>('/api/v1/followers', { params });
  }

  async getFollower(followerId: string) {
    return this.get(`/api/v1/followers/${followerId}`);
  }

  async updateFollower(followerId: string, data: any) {
    return this.put(`/api/v1/followers/${followerId}`, data);
  }

  // Signals API
  async getSignals(params?: {
    page?: number;
    limit?: number;
    provider_id?: string;
    status?: string;
    symbol?: string;
    action?: string;
  }) {
    return this.get<PaginatedResponse<any>>('/api/v1/signals', { params });
  }

  async getSignal(signalId: string) {
    return this.get(`/api/v1/signals/${signalId}`);
  }

  async updateSignal(signalId: string, data: any) {
    return this.put(`/api/v1/signals/${signalId}`, data);
  }

  async cancelSignal(signalId: string) {
    return this.delete(`/api/v1/signals/${signalId}`);
  }

  async getSignalStats(providerId?: string, days: number = 7) {
    return this.get('/api/v1/signals/stats/summary', {
      params: { provider_id: providerId, days }
    });
  }

  // Trades API
  async getTrades(params?: {
    page?: number;
    limit?: number;
    follower_id?: string;
    signal_id?: string;
    status?: string;
  }) {
    return this.get<PaginatedResponse<any>>('/api/v1/trades', { params });
  }

  async getTrade(tradeId: string) {
    return this.get(`/api/v1/trades/${tradeId}`);
  }

  // Users API
  async getUsers(params?: {
    page?: number;
    limit?: number;
    user_type?: string;
    is_active?: boolean;
  }) {
    return this.get<PaginatedResponse<any>>('/api/v1/users', { params });
  }

  async getUser(userId: string) {
    return this.get(`/api/v1/users/${userId}`);
  }

  async updateUser(userId: string, data: any) {
    return this.put(`/api/v1/users/${userId}`, data);
  }

  async deleteUser(userId: string) {
    return this.delete(`/api/v1/users/${userId}`);
  }

  // Analytics API
  async getAnalytics(params?: {
    period?: string;
    provider_id?: string;
    follower_id?: string;
  }) {
    return this.get('/api/v1/analytics', { params });
  }

  async getPerformanceMetrics(params?: {
    provider_id?: string;
    start_date?: string;
    end_date?: string;
  }) {
    return this.get('/api/v1/analytics/performance', { params });
  }

  // Settings API
  async getSettings() {
    return this.get('/api/v1/settings');
  }

  async updateSettings(data: any) {
    return this.put('/api/v1/settings', data);
  }

  // File upload
  async uploadFile(file: File, endpoint: string = '/api/v1/upload') {
    const formData = new FormData();
    formData.append('file', file);

    return this.post(endpoint, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  // WebSocket connection info
  getWebSocketUrl(): string {
    const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsHost = process.env.REACT_APP_WS_URL || `${wsProtocol}//${window.location.host}`;
    return `${wsHost}/ws/signals?token=${this.token}`;
  }
}

// Create and export API client instance
export const apiClient = new ApiClient();
export default apiClient;

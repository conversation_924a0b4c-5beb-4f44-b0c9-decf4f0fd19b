import React, { useState, useEffect } from 'react';
import {
  Grid,
  Paper,
  Typography,
  Box,
  Card,
  CardContent,
  CircularProgress,
  Alert,
  Chip,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  People,
  Signal,
  AccountBalance,
  Refresh,
  Warning,
  CheckCircle
} from '@mui/icons-material';
import { useQuery } from 'react-query';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';

// Services
import { dashboardService } from '../../services/dashboardService';
import { useWebSocket } from '../../contexts/WebSocketContext';

// Components
import StatCard from '../../components/Dashboard/StatCard';
import RealtimeChart from '../../components/Dashboard/RealtimeChart';
import SystemHealth from '../../components/Dashboard/SystemHealth';

interface DashboardStats {
  totalProviders: number;
  activeProviders: number;
  totalFollowers: number;
  activeFollowers: number;
  totalSignals: number;
  signalsToday: number;
  totalTrades: number;
  tradesToday: number;
  systemHealth: {
    api: 'healthy' | 'warning' | 'error';
    database: 'healthy' | 'warning' | 'error';
    redis: 'healthy' | 'warning' | 'error';
    websocket: 'healthy' | 'warning' | 'error';
  };
}

interface RealtimeData {
  timestamp: string;
  signals: number;
  trades: number;
  activeUsers: number;
}

const Dashboard: React.FC = () => {
  const [realtimeData, setRealtimeData] = useState<RealtimeData[]>([]);
  const { socket, isConnected } = useWebSocket();

  // Fetch dashboard stats
  const {
    data: stats,
    isLoading: statsLoading,
    error: statsError,
    refetch: refetchStats
  } = useQuery<DashboardStats>(
    'dashboardStats',
    dashboardService.getStats,
    {
      refetchInterval: 30000, // Refetch every 30 seconds
    }
  );

  // Fetch chart data
  const {
    data: chartData,
    isLoading: chartLoading,
    error: chartError
  } = useQuery(
    'dashboardCharts',
    () => dashboardService.getChartData('24h'),
    {
      refetchInterval: 60000, // Refetch every minute
    }
  );

  // WebSocket real-time updates
  useEffect(() => {
    if (socket && isConnected) {
      socket.on('dashboard_update', (data: RealtimeData) => {
        setRealtimeData(prev => {
          const newData = [...prev, data];
          // Keep only last 50 data points
          return newData.slice(-50);
        });
      });

      socket.on('system_alert', (alert: any) => {
        // Handle system alerts
        console.log('System alert:', alert);
      });

      return () => {
        socket.off('dashboard_update');
        socket.off('system_alert');
      };
    }
  }, [socket, isConnected]);

  const handleRefresh = () => {
    refetchStats();
  };

  if (statsLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (statsError) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Failed to load dashboard data. Please try again.
      </Alert>
    );
  }

  const getHealthColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'success';
      case 'warning': return 'warning';
      case 'error': return 'error';
      default: return 'default';
    }
  };

  const getHealthIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle />;
      case 'warning': return <Warning />;
      case 'error': return <Warning />;
      default: return <Warning />;
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Dashboard
        </Typography>
        <Box display="flex" alignItems="center" gap={2}>
          <Chip
            icon={isConnected ? <CheckCircle /> : <Warning />}
            label={isConnected ? 'Connected' : 'Disconnected'}
            color={isConnected ? 'success' : 'error'}
            size="small"
          />
          <Tooltip title="Refresh Data">
            <IconButton onClick={handleRefresh}>
              <Refresh />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Providers"
            value={stats?.totalProviders || 0}
            subtitle={`${stats?.activeProviders || 0} active`}
            icon={<People />}
            color="primary"
            trend={5.2}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Followers"
            value={stats?.totalFollowers || 0}
            subtitle={`${stats?.activeFollowers || 0} active`}
            icon={<AccountBalance />}
            color="secondary"
            trend={12.8}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Signals Today"
            value={stats?.signalsToday || 0}
            subtitle={`${stats?.totalSignals || 0} total`}
            icon={<Signal />}
            color="info"
            trend={-2.1}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Trades Today"
            value={stats?.tradesToday || 0}
            subtitle={`${stats?.totalTrades || 0} total`}
            icon={<TrendingUp />}
            color="success"
            trend={8.4}
          />
        </Grid>
      </Grid>

      {/* System Health */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" mb={2}>
              System Health
            </Typography>
            <Grid container spacing={2}>
              {stats?.systemHealth && Object.entries(stats.systemHealth).map(([service, status]) => (
                <Grid item xs={6} sm={3} key={service}>
                  <Box display="flex" alignItems="center" gap={1}>
                    {getHealthIcon(status)}
                    <Typography variant="body2" sx={{ textTransform: 'capitalize' }}>
                      {service}
                    </Typography>
                    <Chip
                      label={status}
                      size="small"
                      color={getHealthColor(status) as any}
                    />
                  </Box>
                </Grid>
              ))}
            </Grid>
          </Paper>
        </Grid>
      </Grid>

      {/* Charts */}
      <Grid container spacing={3}>
        {/* Real-time Activity */}
        <Grid item xs={12} lg={8}>
          <Paper sx={{ p: 2, height: 400 }}>
            <Typography variant="h6" mb={2}>
              Real-time Activity
            </Typography>
            {realtimeData.length > 0 ? (
              <ResponsiveContainer width="100%" height="90%">
                <LineChart data={realtimeData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="timestamp" 
                    tickFormatter={(value) => new Date(value).toLocaleTimeString()}
                  />
                  <YAxis />
                  <RechartsTooltip 
                    labelFormatter={(value) => new Date(value).toLocaleString()}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="signals" 
                    stroke="#8884d8" 
                    name="Signals"
                    strokeWidth={2}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="trades" 
                    stroke="#82ca9d" 
                    name="Trades"
                    strokeWidth={2}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="activeUsers" 
                    stroke="#ffc658" 
                    name="Active Users"
                    strokeWidth={2}
                  />
                </LineChart>
              </ResponsiveContainer>
            ) : (
              <Box display="flex" justifyContent="center" alignItems="center" height="90%">
                <Typography color="textSecondary">
                  Waiting for real-time data...
                </Typography>
              </Box>
            )}
          </Paper>
        </Grid>

        {/* Performance Summary */}
        <Grid item xs={12} lg={4}>
          <Paper sx={{ p: 2, height: 400 }}>
            <Typography variant="h6" mb={2}>
              Performance Summary
            </Typography>
            {chartLoading ? (
              <Box display="flex" justifyContent="center" alignItems="center" height="90%">
                <CircularProgress />
              </Box>
            ) : chartError ? (
              <Alert severity="error">Failed to load chart data</Alert>
            ) : chartData ? (
              <ResponsiveContainer width="100%" height="90%">
                <PieChart>
                  <Pie
                    data={chartData.performance}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {chartData.performance?.map((entry: any, index: number) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <RechartsTooltip />
                </PieChart>
              </ResponsiveContainer>
            ) : (
              <Typography color="textSecondary">No data available</Typography>
            )}
          </Paper>
        </Grid>

        {/* Recent Activity */}
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" mb={2}>
              Signal Activity (Last 24 Hours)
            </Typography>
            {chartLoading ? (
              <Box display="flex" justifyContent="center" alignItems="center" height="300px">
                <CircularProgress />
              </Box>
            ) : chartData?.hourlyActivity ? (
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={chartData.hourlyActivity}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="hour" />
                  <YAxis />
                  <RechartsTooltip />
                  <Bar dataKey="signals" fill="#8884d8" name="Signals" />
                  <Bar dataKey="trades" fill="#82ca9d" name="Trades" />
                </BarChart>
              </ResponsiveContainer>
            ) : (
              <Typography color="textSecondary">No activity data available</Typography>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;

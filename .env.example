# Copy Trading System Environment Configuration
# Copy this file to .env and update with your actual values

# Application Settings
ENVIRONMENT=development
APP_NAME=Copy Trading System
VERSION=2.0.0
DEBUG=true
HOST=0.0.0.0
PORT=8000

# Security
SECRET_KEY=your-super-secret-key-change-in-production-min-32-chars
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Database Configuration
DATABASE_URL=postgresql://copytrading:password@localhost:5432/copytrading_db
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_DB=0

# CORS Settings
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080,https://yourdomain.com

# API Keys
INTERNAL_API_TOKEN=internal-api-token-change-in-production

# MT5 Settings
MT5_POLL_INTERVAL=1
MT5_MESSAGE_TTL=300
MAX_SIGNALS_PER_DAY=100
SIGNAL_EXPIRY_MINUTES=60

# Risk Management
MAX_RISK_PER_TRADE=5.0
MAX_DAILY_LOSS=1000.0
MAX_DRAWDOWN=20.0
MIN_BALANCE_REQUIRED=100.0

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_PER_HOUR=1000

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_TLS=true

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090
LOG_LEVEL=INFO

# File Storage
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760

# WebSocket
WS_HEARTBEAT_INTERVAL=30
WS_MAX_CONNECTIONS=1000

# Performance
WORKER_PROCESSES=1
WORKER_CONNECTIONS=1000
KEEPALIVE_TIMEOUT=5

# Production overrides (uncomment for production)
# ENVIRONMENT=production
# DEBUG=false
# LOG_LEVEL=INFO
# WORKER_PROCESSES=4
# SECRET_KEY=production-secret-key-min-32-chars
# DATABASE_URL=***********************************/copytrading
# REDIS_URL=redis://prod-redis:6379/0

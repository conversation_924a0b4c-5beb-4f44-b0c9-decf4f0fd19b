#!/bin/bash

# Copy Trading System - Local Development Setup
# This script sets up and runs the system locally without Docker

echo "🚀 Copy Trading System - Local Development Setup"
echo "================================================"

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is required but not installed."
    exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is required but not installed."
    exit 1
fi

# Create virtual environment for backend
echo "📦 Setting up Python virtual environment..."
cd backend
if [ ! -d "venv" ]; then
    python3 -m venv venv
fi

# Activate virtual environment
source venv/bin/activate

# Install Python dependencies
echo "📦 Installing Python dependencies..."
pip install -r requirements.txt

# Set environment variables
export DATABASE_URL="sqlite:///./copytrading.db"
export REDIS_URL="redis://localhost:6379/0"
export SECRET_KEY="dev-secret-key-for-local-development"
export DEBUG="true"
export ENVIRONMENT="development"

# Create SQLite database (fallback if PostgreSQL not available)
echo "🗄️ Setting up database..."
python -c "
import sqlite3
conn = sqlite3.connect('copytrading.db')
conn.execute('''
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    email TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    user_type TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
''')
conn.execute('''
CREATE TABLE IF NOT EXISTS providers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER REFERENCES users(id),
    strategy_name TEXT,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
''')
conn.execute('''
CREATE TABLE IF NOT EXISTS followers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER REFERENCES users(id),
    risk_level REAL DEFAULT 1.0,
    max_drawdown REAL DEFAULT 20.0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
''')
conn.execute('''
CREATE TABLE IF NOT EXISTS signals (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    provider_id INTEGER REFERENCES providers(id),
    symbol TEXT NOT NULL,
    action TEXT NOT NULL,
    volume REAL NOT NULL,
    price REAL,
    stop_loss REAL,
    take_profit REAL,
    status TEXT DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
''')
conn.commit()
conn.close()
print('Database initialized successfully!')
"

# Create admin user
echo "👤 Creating admin user..."
python -c "
import sqlite3
import hashlib

conn = sqlite3.connect('copytrading.db')
password_hash = hashlib.sha256('admin123'.encode()).hexdigest()

try:
    conn.execute('''
    INSERT OR IGNORE INTO users (email, password_hash, user_type)
    VALUES ('<EMAIL>', ?, 'admin')
    ''', (password_hash,))
    conn.commit()
    print('Admin user created: <EMAIL> / admin123')
except Exception as e:
    print(f'Admin user already exists or error: {e}')
finally:
    conn.close()
"

# Start backend server
echo "🚀 Starting backend server..."
echo "Backend will be available at: http://localhost:8000"
echo "API Documentation: http://localhost:8000/docs"
echo ""

# Start backend in background
nohup python main.py > backend.log 2>&1 &
BACKEND_PID=$!
echo "Backend started with PID: $BACKEND_PID"

# Go back to root directory
cd ..

# Setup and start user frontend
echo "🌐 Setting up User Frontend..."
cd frontend/user-app

# Install Node.js dependencies
if [ ! -d "node_modules" ]; then
    echo "📦 Installing Node.js dependencies..."
    npm install
fi

# Set environment variables for frontend
export REACT_APP_API_URL="http://localhost:8000"
export NODE_ENV="development"

# Start frontend in background
echo "🚀 Starting User Frontend..."
echo "User Frontend will be available at: http://localhost:3000"
nohup npm start > frontend.log 2>&1 &
FRONTEND_PID=$!
echo "Frontend started with PID: $FRONTEND_PID"

# Go back to root
cd ../..

# Save PIDs for cleanup
echo $BACKEND_PID > backend.pid
echo $FRONTEND_PID > frontend.pid

echo ""
echo "✅ Copy Trading System is starting up!"
echo "================================================"
echo "🌐 User Frontend:     http://localhost:3000"
echo "🔧 Backend API:       http://localhost:8000"
echo "📚 API Docs:          http://localhost:8000/docs"
echo ""
echo "👤 Demo Login:"
echo "   Email:    <EMAIL>"
echo "   Password: admin123"
echo ""
echo "📝 Logs:"
echo "   Backend:  backend/backend.log"
echo "   Frontend: frontend/user-app/frontend.log"
echo ""
echo "🛑 To stop the system, run: ./stop_local.sh"
echo ""
echo "⏳ Please wait 30-60 seconds for services to fully start..."

# Wait a bit and check if services are running
sleep 10

# Check backend
if curl -s http://localhost:8000/health > /dev/null; then
    echo "✅ Backend is running successfully!"
else
    echo "⚠️  Backend may still be starting up..."
fi

# Check frontend (this might take longer)
echo "⏳ Frontend is starting up (this may take 1-2 minutes)..."

echo ""
echo "🎉 Setup complete! Your Copy Trading System is ready to use."

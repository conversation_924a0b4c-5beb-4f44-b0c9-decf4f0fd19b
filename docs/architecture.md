# System Architecture Documentation

## Overview

The Copy Trading System is a high-performance, scalable platform designed to facilitate real-time signal distribution from trading providers to followers using MetaTrader 5 (MT5) Expert Advisors.

## Architecture Principles

### 1. Microservices Architecture
- **Separation of Concerns**: Each service handles specific business logic
- **Independent Scaling**: Services can be scaled independently based on load
- **Technology Diversity**: Different services can use optimal technologies
- **Fault Isolation**: Failure in one service doesn't affect others

### 2. Event-Driven Design
- **Asynchronous Processing**: Non-blocking signal processing
- **Real-time Updates**: WebSocket connections for web clients
- **Message Queuing**: Redis-based message queuing for MT5 clients

### 3. High Availability
- **Load Balancing**: Multiple backend instances with Nginx
- **Database Replication**: PostgreSQL read replicas
- **Caching Strategy**: Multi-layer caching with Redis
- **Graceful Degradation**: System continues operating with reduced functionality

## System Components

### Core Services

#### 1. Backend API (FastAPI)
```
Location: backend/
Technology: Python 3.11, FastAPI, SQLAlchemy
Purpose: Core business logic, API endpoints, signal processing
```

**Key Features:**
- RESTful API endpoints
- JWT authentication
- Real-time WebSocket connections
- Signal processing and distribution
- Risk management
- Performance analytics

**Endpoints:**
- `/api/v1/auth/*` - Authentication
- `/api/v1/signals/*` - Signal management
- `/api/v1/providers/*` - Provider management
- `/api/v1/followers/*` - Follower management
- `/api/v1/mt5/*` - MT5 specific endpoints

#### 2. Database Layer (PostgreSQL)
```
Location: Database server
Technology: PostgreSQL 15+
Purpose: Persistent data storage
```

**Schema Design:**
- **Users**: User accounts and authentication
- **Providers**: Trading signal providers
- **Followers**: Signal followers
- **Signals**: Trading signals and metadata
- **Trades**: Executed trades and results
- **Subscriptions**: Provider-follower relationships
- **Performance Metrics**: Trading performance data

#### 3. Cache Layer (Redis)
```
Location: Redis server
Technology: Redis 7+
Purpose: Caching, session storage, message queuing
```

**Usage Patterns:**
- **Signal Caching**: Fast signal retrieval
- **Session Storage**: User sessions and tokens
- **Message Queuing**: MT5 client message queues
- **Rate Limiting**: API rate limiting counters
- **Real-time Data**: Live trading data

#### 4. Provider EA (MT5 Expert Advisor)
```
Location: ea/provider/
Technology: MQL5
Purpose: Capture and send trading signals
```

**Functionality:**
- Position monitoring
- Signal generation
- Partial close detection
- Pending order tracking
- Risk validation
- HTTP communication

#### 5. Follower EA (MT5 Expert Advisor)
```
Location: ea/follower/
Technology: MQL5
Purpose: Receive and execute trading signals
```

**Functionality:**
- Signal reception via HTTP polling
- Trade execution
- Risk management
- Position tracking
- Performance monitoring

### Supporting Services

#### 6. Admin Dashboard (React)
```
Location: frontend/admin-dashboard/
Technology: React 18, TypeScript, Material-UI
Purpose: System administration and monitoring
```

#### 7. Reverse Proxy (Nginx)
```
Location: infrastructure/nginx/
Technology: Nginx
Purpose: Load balancing, SSL termination, static file serving
```

#### 8. Monitoring Stack
```
Components: Prometheus, Grafana, ELK Stack
Purpose: System monitoring, logging, alerting
```

## Data Flow Architecture

### Signal Distribution Flow

```mermaid
graph TD
    A[Provider EA] -->|HTTP POST| B[Backend API]
    B --> C[Signal Validation]
    C --> D[Risk Management]
    D --> E[Signal Processing]
    E --> F[Database Storage]
    E --> G[Redis Caching]
    G --> H[Message Queuing]
    H --> I[Follower EA Polling]
    I --> J[Trade Execution]
    J --> K[Result Reporting]
    K --> B
```

### Authentication Flow

```mermaid
graph TD
    A[Client] -->|Login Request| B[Auth Service]
    B --> C[User Validation]
    C --> D[JWT Generation]
    D --> E[Token Response]
    E --> A
    A -->|API Request + Token| F[Protected Endpoint]
    F --> G[Token Validation]
    G --> H[Business Logic]
```

## Communication Patterns

### 1. Provider → Backend
- **Protocol**: HTTP/HTTPS
- **Method**: POST requests
- **Authentication**: API Keys or JWT tokens
- **Data Format**: JSON
- **Frequency**: Real-time (on trade events)

### 2. Backend → Followers
- **Protocol**: HTTP/HTTPS (polling)
- **Method**: GET requests
- **Authentication**: API Keys or JWT tokens
- **Data Format**: JSON
- **Frequency**: 1-second polling interval

### 3. Web Dashboard → Backend
- **Protocol**: HTTP/HTTPS + WebSocket
- **Authentication**: JWT tokens
- **Data Format**: JSON
- **Features**: Real-time updates via WebSocket

## Scalability Design

### Horizontal Scaling

#### Backend Services
```yaml
Scaling Strategy: Load-balanced instances
Load Balancer: Nginx
Session Storage: Redis (shared)
Database: Connection pooling
```

#### Database Scaling
```yaml
Primary: Write operations
Replicas: Read operations
Partitioning: By provider_id or date
Indexing: Optimized for query patterns
```

#### Cache Scaling
```yaml
Redis Cluster: Multiple nodes
Sharding: By user_id or provider_id
Replication: Master-slave setup
```

### Performance Optimizations

#### Database
- **Indexing**: Strategic indexes on frequently queried columns
- **Partitioning**: Table partitioning for large datasets
- **Connection Pooling**: Optimized connection management
- **Query Optimization**: Efficient SQL queries

#### Caching
- **Multi-layer Caching**: Application, Redis, CDN
- **Cache Invalidation**: Smart cache invalidation strategies
- **Compression**: Data compression for large objects

#### API
- **Response Compression**: Gzip compression
- **Pagination**: Efficient data pagination
- **Rate Limiting**: Prevent API abuse
- **Async Processing**: Non-blocking operations

## Security Architecture

### Authentication & Authorization
- **JWT Tokens**: Stateless authentication
- **API Keys**: Long-lived tokens for EAs
- **Role-based Access**: Provider, Follower, Admin roles
- **Token Refresh**: Secure token renewal

### Data Protection
- **Encryption at Rest**: Database encryption
- **Encryption in Transit**: TLS/SSL for all communications
- **Input Validation**: Comprehensive request validation
- **SQL Injection Prevention**: Parameterized queries

### Network Security
- **Firewall Rules**: Restricted network access
- **Rate Limiting**: DDoS protection
- **CORS Policy**: Cross-origin request control
- **SSL/TLS**: End-to-end encryption

## Deployment Architecture

### Development Environment
```yaml
Services: Docker Compose
Database: PostgreSQL container
Cache: Redis container
Monitoring: Local Prometheus/Grafana
```

### Production Environment
```yaml
Orchestration: Docker Swarm or Kubernetes
Load Balancer: Nginx or cloud load balancer
Database: Managed PostgreSQL service
Cache: Managed Redis service
Monitoring: Prometheus + Grafana + ELK
```

### Infrastructure as Code
```yaml
Configuration: Docker Compose files
Secrets Management: Environment variables
Backup Strategy: Automated database backups
Disaster Recovery: Multi-region deployment
```

## Quality Assurance

### Testing Strategy
- **Unit Tests**: Individual component testing
- **Integration Tests**: Service interaction testing
- **Load Tests**: Performance and scalability testing
- **Security Tests**: Vulnerability assessments

### Monitoring & Alerting
- **System Metrics**: CPU, memory, disk, network
- **Application Metrics**: Response times, error rates
- **Business Metrics**: Signal latency, trade success rates
- **Alerting**: Automated alerts for critical issues

### Logging Strategy
- **Structured Logging**: JSON-formatted logs
- **Log Aggregation**: ELK stack for centralized logging
- **Log Retention**: Configurable retention policies
- **Log Analysis**: Automated log analysis and alerting

## Future Architecture Considerations

### Microservices Evolution
- **Service Mesh**: Istio for service communication
- **API Gateway**: Kong or AWS API Gateway
- **Event Sourcing**: Event-driven architecture
- **CQRS**: Command Query Responsibility Segregation

### Cloud-Native Features
- **Kubernetes**: Container orchestration
- **Service Discovery**: Automatic service discovery
- **Circuit Breakers**: Fault tolerance patterns
- **Auto-scaling**: Automatic resource scaling

### Advanced Analytics
- **Machine Learning**: Signal analysis and prediction
- **Real-time Analytics**: Stream processing
- **Data Lake**: Historical data analysis
- **AI-powered Risk Management**: Intelligent risk assessment

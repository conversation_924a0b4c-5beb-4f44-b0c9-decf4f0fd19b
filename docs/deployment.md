# Deployment Guide

## Overview

This guide covers the complete deployment process for the Copy Trading System, from development environment setup to production deployment with monitoring and maintenance procedures.

## Prerequisites

### System Requirements

#### Minimum Requirements
- **CPU**: 4 cores, 2.4 GHz
- **RAM**: 8 GB
- **Storage**: 100 GB SSD
- **Network**: 100 Mbps, low latency
- **OS**: Ubuntu 22.04 LTS or CentOS 8+

#### Recommended Requirements
- **CPU**: 8 cores, 3.0 GHz
- **RAM**: 16 GB
- **Storage**: 500 GB SSD
- **Network**: 1 Gbps, low latency
- **OS**: Ubuntu 22.04 LTS

#### Production Requirements
- **CPU**: 16 cores, 3.2 GHz
- **RAM**: 32 GB
- **Storage**: 1 TB NVMe SSD
- **Network**: 10 Gbps, ultra-low latency
- **OS**: Ubuntu 22.04 LTS

### Software Dependencies

#### Required Software
- Docker 24.0+
- Docker Compose 2.0+
- Git 2.30+
- SSL certificates (Let's Encrypt or commercial)

#### Optional Software
- Kubernetes 1.28+ (for advanced deployments)
- Terraform (for infrastructure as code)
- Ansible (for configuration management)

## Development Environment

### Quick Start

1. **Clone Repository**
```bash
git clone https://github.com/your-org/copy-trading-system.git
cd copy-trading-system
```

2. **Environment Configuration**
```bash
# Copy environment template
cp .env.example .env

# Edit configuration
nano .env
```

3. **Start Services**
```bash
# Start all services
docker-compose up -d

# Check service status
docker-compose ps
```

4. **Initialize Database**
```bash
# Run migrations
docker-compose exec backend alembic upgrade head

# Create admin user
docker-compose exec backend python scripts/create_admin.py
```

### Service Access

- **API Documentation**: http://localhost:8000/docs
- **Admin Dashboard**: http://localhost:3000
- **Grafana Monitoring**: http://localhost:3001 (admin/admin)
- **Database**: localhost:5432 (copytrading/password)
- **Redis**: localhost:6379

### Development Workflow

```bash
# View logs
docker-compose logs -f backend

# Run tests
docker-compose exec backend pytest

# Database shell
docker-compose exec postgres psql -U copytrading -d copytrading_db

# Redis shell
docker-compose exec redis redis-cli
```

## Staging Environment

### Infrastructure Setup

1. **Server Preparation**
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.21.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Create application user
sudo useradd -m -s /bin/bash copytrading
sudo usermod -aG docker copytrading
```

2. **Application Deployment**
```bash
# Switch to application user
sudo su - copytrading

# Clone repository
git clone https://github.com/your-org/copy-trading-system.git
cd copy-trading-system

# Configure environment
cp .env.example .env.staging
nano .env.staging

# Deploy staging environment
docker-compose -f docker-compose.staging.yml up -d
```

3. **SSL Configuration**
```bash
# Install Certbot
sudo apt install certbot

# Obtain SSL certificate
sudo certbot certonly --standalone -d staging.copytrading.com

# Configure Nginx
sudo cp infrastructure/nginx/nginx.staging.conf /etc/nginx/sites-available/
sudo ln -s /etc/nginx/sites-available/nginx.staging.conf /etc/nginx/sites-enabled/
sudo nginx -t && sudo systemctl reload nginx
```

### Staging Configuration

```yaml
# .env.staging
ENVIRONMENT=staging
DEBUG=false
DATABASE_URL=postgresql://copytrading:secure_password@localhost:5432/copytrading_staging
REDIS_URL=redis://localhost:6379/1
SECRET_KEY=staging-secret-key-32-chars-minimum
ALLOWED_ORIGINS=https://staging.copytrading.com
```

## Production Deployment

### Infrastructure Planning

#### Single Server Deployment
```
┌─────────────────────────────────────┐
│           Production Server         │
├─────────────────────────────────────┤
│  Nginx (Load Balancer/SSL)         │
│  ├── Backend API (3 instances)     │
│  ├── Admin Dashboard               │
│  ├── PostgreSQL (Primary)          │
│  ├── Redis (Cache/Queue)           │
│  └── Monitoring Stack              │
└─────────────────────────────────────┘
```

#### Multi-Server Deployment
```
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│  Load Balancer  │  │   App Servers   │  │   Database      │
│                 │  │                 │  │                 │
│  Nginx/HAProxy  │  │  Backend API    │  │  PostgreSQL     │
│  SSL Termination│  │  Admin Dashboard│  │  (Primary +     │
│                 │  │  (Multiple)     │  │   Replicas)     │
└─────────────────┘  └─────────────────┘  └─────────────────┘
         │                     │                     │
         └─────────────────────┼─────────────────────┘
                               │
                    ┌─────────────────┐
                    │     Cache       │
                    │                 │
                    │  Redis Cluster  │
                    │  (Multiple)     │
                    └─────────────────┘
```

### Production Setup

1. **Server Preparation**
```bash
# System hardening
sudo ufw enable
sudo ufw allow 22,80,443/tcp

# Install fail2ban
sudo apt install fail2ban
sudo systemctl enable fail2ban

# Configure automatic updates
sudo apt install unattended-upgrades
sudo dpkg-reconfigure unattended-upgrades
```

2. **Database Setup**
```bash
# Install PostgreSQL
sudo apt install postgresql-14 postgresql-contrib

# Configure PostgreSQL
sudo -u postgres createuser copytrading
sudo -u postgres createdb copytrading_prod -O copytrading
sudo -u postgres psql -c "ALTER USER copytrading PASSWORD 'secure_production_password';"

# Configure connection limits and performance
sudo nano /etc/postgresql/14/main/postgresql.conf
```

3. **Application Deployment**
```bash
# Production environment file
cat > .env.production << EOF
ENVIRONMENT=production
DEBUG=false
DATABASE_URL=postgresql://copytrading:secure_password@localhost:5432/copytrading_prod
REDIS_URL=redis://localhost:6379/0
SECRET_KEY=production-secret-key-minimum-32-characters
ALLOWED_ORIGINS=https://copytrading.com,https://www.copytrading.com
WORKER_PROCESSES=4
LOG_LEVEL=INFO
EOF

# Deploy production
docker-compose -f docker-compose.prod.yml up -d

# Run database migrations
docker-compose -f docker-compose.prod.yml exec backend alembic upgrade head
```

4. **Load Balancer Configuration**
```nginx
# /etc/nginx/sites-available/copytrading.com
upstream backend {
    least_conn;
    server 127.0.0.1:8001 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:8002 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:8003 max_fails=3 fail_timeout=30s;
}

server {
    listen 443 ssl http2;
    server_name copytrading.com www.copytrading.com;
    
    ssl_certificate /etc/letsencrypt/live/copytrading.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/copytrading.com/privkey.pem;
    
    location /api/ {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### Scaling Configuration

#### Horizontal Scaling
```bash
# Scale backend services
docker-compose -f docker-compose.prod.yml up -d --scale backend=3

# Scale with Docker Swarm
docker swarm init
docker stack deploy -c docker-compose.prod.yml copytrading
```

#### Database Scaling
```sql
-- Configure read replicas
-- Primary database (write operations)
CREATE USER replicator WITH REPLICATION ENCRYPTED PASSWORD 'replica_password';

-- Replica configuration in postgresql.conf
wal_level = replica
max_wal_senders = 3
wal_keep_segments = 64
```

## Monitoring and Logging

### Monitoring Stack Setup

1. **Prometheus Configuration**
```yaml
# infrastructure/prometheus/prometheus.prod.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'backend'
    static_configs:
      - targets: ['backend:8000']
  
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
  
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
```

2. **Grafana Dashboards**
```bash
# Import pre-configured dashboards
docker-compose exec grafana grafana-cli plugins install grafana-piechart-panel
```

3. **Log Aggregation**
```yaml
# infrastructure/filebeat/filebeat.yml
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /var/log/copytrading/*.log
  fields:
    service: copytrading
    environment: production

output.elasticsearch:
  hosts: ["elasticsearch:9200"]
```

### Alerting Rules

```yaml
# infrastructure/prometheus/alerts.yml
groups:
- name: copytrading
  rules:
  - alert: HighErrorRate
    expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
    for: 5m
    annotations:
      summary: High error rate detected
  
  - alert: DatabaseDown
    expr: up{job="postgres"} == 0
    for: 1m
    annotations:
      summary: Database is down
```

## Security Configuration

### SSL/TLS Setup

1. **Let's Encrypt Certificate**
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d copytrading.com -d www.copytrading.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

2. **Security Headers**
```nginx
# Add to Nginx configuration
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;
```

### Firewall Configuration

```bash
# UFW rules
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw enable

# Fail2ban configuration
sudo nano /etc/fail2ban/jail.local
```

### Database Security

```sql
-- Create application-specific users
CREATE USER copytrading_app WITH PASSWORD 'app_password';
CREATE USER copytrading_readonly WITH PASSWORD 'readonly_password';

-- Grant minimal permissions
GRANT CONNECT ON DATABASE copytrading_prod TO copytrading_app;
GRANT USAGE ON SCHEMA public TO copytrading_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO copytrading_app;

-- Read-only user for analytics
GRANT CONNECT ON DATABASE copytrading_prod TO copytrading_readonly;
GRANT USAGE ON SCHEMA public TO copytrading_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO copytrading_readonly;
```

## Backup and Recovery

### Database Backup

```bash
#!/bin/bash
# backup_database.sh

BACKUP_DIR="/backups/database"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="copytrading_prod"

# Create backup
pg_dump -h localhost -U copytrading -d $DB_NAME | gzip > $BACKUP_DIR/backup_$DATE.sql.gz

# Upload to S3 (optional)
aws s3 cp $BACKUP_DIR/backup_$DATE.sql.gz s3://copytrading-backups/database/

# Cleanup old backups (keep 30 days)
find $BACKUP_DIR -name "backup_*.sql.gz" -mtime +30 -delete
```

### Application Backup

```bash
#!/bin/bash
# backup_application.sh

BACKUP_DIR="/backups/application"
DATE=$(date +%Y%m%d_%H%M%S)

# Backup configuration
tar -czf $BACKUP_DIR/config_$DATE.tar.gz /opt/copytrading/.env* /opt/copytrading/docker-compose*.yml

# Backup logs
tar -czf $BACKUP_DIR/logs_$DATE.tar.gz /var/log/copytrading/

# Upload to S3
aws s3 sync $BACKUP_DIR s3://copytrading-backups/application/
```

### Recovery Procedures

```bash
# Database recovery
gunzip -c backup_20231201_120000.sql.gz | psql -h localhost -U copytrading -d copytrading_prod

# Application recovery
docker-compose -f docker-compose.prod.yml down
tar -xzf config_20231201_120000.tar.gz -C /
docker-compose -f docker-compose.prod.yml up -d
```

## Maintenance Procedures

### Regular Maintenance Tasks

1. **Daily Tasks**
```bash
# Check system health
docker-compose ps
systemctl status nginx postgresql redis

# Check disk space
df -h

# Review error logs
tail -100 /var/log/copytrading/error.log
```

2. **Weekly Tasks**
```bash
# Update system packages
sudo apt update && sudo apt upgrade

# Analyze database performance
docker-compose exec postgres psql -U copytrading -d copytrading_prod -c "SELECT * FROM pg_stat_activity;"

# Clean up old logs
find /var/log/copytrading -name "*.log" -mtime +7 -delete
```

3. **Monthly Tasks**
```bash
# Database maintenance
docker-compose exec postgres psql -U copytrading -d copytrading_prod -c "VACUUM ANALYZE;"

# Update Docker images
docker-compose pull
docker-compose up -d

# Security updates
sudo unattended-upgrades
```

### Performance Tuning

#### Database Optimization
```sql
-- Analyze query performance
SELECT query, mean_time, calls FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10;

-- Update table statistics
ANALYZE;

-- Reindex if needed
REINDEX DATABASE copytrading_prod;
```

#### Application Optimization
```bash
# Monitor resource usage
docker stats

# Optimize container resources
docker-compose -f docker-compose.prod.yml up -d --scale backend=4
```

## Troubleshooting

### Common Issues

1. **High CPU Usage**
```bash
# Check processes
top -p $(pgrep -d',' -f copytrading)

# Scale backend services
docker-compose -f docker-compose.prod.yml up -d --scale backend=6
```

2. **Database Connection Issues**
```bash
# Check connections
docker-compose exec postgres psql -U copytrading -c "SELECT * FROM pg_stat_activity;"

# Increase connection limits
echo "max_connections = 200" >> /etc/postgresql/14/main/postgresql.conf
sudo systemctl restart postgresql
```

3. **Memory Issues**
```bash
# Check memory usage
free -h
docker stats --no-stream

# Optimize Redis memory
redis-cli CONFIG SET maxmemory 2gb
redis-cli CONFIG SET maxmemory-policy allkeys-lru
```

### Emergency Procedures

1. **Service Recovery**
```bash
# Restart all services
docker-compose -f docker-compose.prod.yml restart

# Emergency database recovery
sudo systemctl stop postgresql
sudo -u postgres pg_resetwal /var/lib/postgresql/14/main
sudo systemctl start postgresql
```

2. **Rollback Deployment**
```bash
# Rollback to previous version
git checkout previous-stable-tag
docker-compose -f docker-compose.prod.yml up -d
```

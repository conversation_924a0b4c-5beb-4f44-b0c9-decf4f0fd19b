# Expert Advisor Development Guide

## Overview

This guide covers the development, configuration, and deployment of MetaTrader 5 Expert Advisors (EAs) for the Copy Trading System. The system includes two main EAs: Provider EA (signal generator) and Follower EA (signal receiver).

## Architecture Overview

### Provider EA
- **Purpose**: Capture trading signals from provider's account
- **Location**: `ea/provider/ProviderEA.mq5`
- **Communication**: HTTP POST to backend API
- **Functionality**: Position monitoring, signal generation, risk validation

### Follower EA
- **Purpose**: Receive and execute trading signals
- **Location**: `ea/follower/FollowerEA.mq5`
- **Communication**: HTTP polling from backend API
- **Functionality**: Signal reception, trade execution, risk management

## Development Environment Setup

### Prerequisites

1. **MetaTrader 5 Terminal**
   - Download from MetaQuotes website
   - Install and configure with demo account

2. **MetaEditor IDE**
   - Included with MT5 terminal
   - Used for MQL5 development

3. **Development Tools**
   - Git for version control
   - Text editor (VS Code recommended)
   - API testing tools (Postman, curl)

### Project Structure

```
ea/
├── includes/
│   ├── SignalData.mqh          # Signal data structures
│   └── Common.mqh              # Common utilities
├── provider/
│   ├── ProviderEA.mq5          # Main Provider EA
│   └── includes/
│       ├── HttpClient.mqh      # HTTP communication
│       ├── SignalManager.mqh   # Signal management
│       ├── ConfigManager.mqh   # Configuration
│       └── Logger.mqh          # Logging utilities
├── follower/
│   ├── FollowerEA.mq5          # Main Follower EA
│   └── includes/
│       ├── HttpPollingClient.mqh # HTTP polling
│       ├── TradeExecutor.mqh   # Trade execution
│       ├── RiskManager.mqh     # Risk management
│       └── Logger.mqh          # Logging utilities
├── config/
│   └── ea_config_template.ini  # Configuration template
└── tests/
    └── test_ea_functionality.mq5 # EA testing script
```

## Provider EA Development

### Core Functionality

#### 1. Position Monitoring

```mql5
void OnTick() {
    // Check for new positions
    CheckNewPositions();
    
    // Check for closed positions
    CheckClosedPositions();
    
    // Check for modified positions
    CheckModifiedPositions();
    
    // Check for pending orders
    if(SEND_PENDING_ORDERS) {
        CheckPendingOrders();
    }
    
    // Check pending orders that became positions
    CheckPendingOrdersToPositions();
}
```

#### 2. Signal Generation

```mql5
void ProcessNewPosition() {
    ulong ticket = PositionGetInteger(POSITION_TICKET);
    
    // Check if already processed
    if(IsPositionAlreadyProcessed(ticket)) {
        return;
    }
    
    // Create signal
    SignalData signal;
    if(CSignalDataHelper::CreateSignalFromPosition(ticket, signal, PROVIDER_ID)) {
        // Validate signal
        if(CSignalDataHelper::ValidateSignal(signal)) {
            // Send signal
            if(signalManager.SendSignal(signal)) {
                AddProcessedPosition(ticket);
                dailySignalCount++;
            }
        }
    }
}
```

#### 3. Partial Close Handling

```mql5
void ProcessPartialClose(ulong positionId, double closedVolume) {
    SignalData signal;
    if(CSignalDataHelper::CreatePartialCloseSignal(positionId, closedVolume, signal, PROVIDER_ID)) {
        if(CSignalDataHelper::ValidateSignal(signal)) {
            if(signalManager.SendSignal(signal)) {
                dailySignalCount++;
            }
        }
    }
}
```

### Configuration

#### EA Input Parameters

```mql5
// API Configuration
input string API_BASE_URL = "https://api.copytrading.com";
input string API_KEY = "";
input string PROVIDER_ID = "";

// Signal Settings
input bool AUTO_SEND_SIGNALS = true;
input bool SEND_PENDING_ORDERS = false;
input bool VALIDATE_SIGNALS = true;
input int MAX_DAILY_SIGNALS = 50;
input double MAX_SIGNAL_VOLUME = 10.0;

// Risk Management
input double MIN_PROFIT_POINTS = 10.0;
input double MIN_VOLUME = 0.01;

// System Settings
input int HEARTBEAT_INTERVAL = 30;
input bool ENABLE_LOGGING = true;
input int LOG_LEVEL = 2; // 0=Error, 1=Warning, 2=Info, 3=Debug
```

## Follower EA Development

### Core Functionality

#### 1. Signal Reception

```mql5
void OnTick() {
    // Poll for new signals
    if(pollingClient != NULL) {
        pollingClient.Poll();
        
        // Process received signals
        SignalData signal;
        while(pollingClient.GetNextMessage(signal)) {
            ProcessSignal(signal);
        }
    }
    
    // Monitor performance
    MonitorPerformance();
}
```

#### 2. Trade Execution

```mql5
void ProcessSignal(const SignalData &signal) {
    // Validate signal
    if(!ValidateSignalSafety(signal)) {
        return;
    }
    
    // Check risk management
    if(!riskManager.CheckSignalRisk(signal)) {
        return;
    }
    
    // Execute based on action
    ExecutionResult result;
    switch(signal.action) {
        case "open":
            tradeExecutor.ExecuteOpenSignal(signal, result);
            break;
        case "close":
            if(signal.is_partial_close) {
                tradeExecutor.ExecutePartialCloseSignal(signal, result);
            } else {
                tradeExecutor.ExecuteCloseSignal(signal, result);
            }
            break;
        case "pending":
            tradeExecutor.ExecutePendingOrderSignal(signal, result);
            break;
    }
    
    // Report execution result
    ReportExecutionResult(result);
}
```

#### 3. Risk Management

```mql5
bool CheckSignalRisk(const SignalData &signal) {
    // Check daily loss limit
    if(GetDailyLoss() >= MAX_DAILY_LOSS) {
        return false;
    }
    
    // Check maximum positions
    if(PositionsTotal() >= MAX_OPEN_POSITIONS) {
        return false;
    }
    
    // Check margin requirements
    double requiredMargin = CalculateRequiredMargin(signal);
    if(requiredMargin > AccountInfoDouble(ACCOUNT_MARGIN_FREE)) {
        return false;
    }
    
    return true;
}
```

### Configuration

#### EA Input Parameters

```mql5
// API Configuration
input string API_BASE_URL = "https://api.copytrading.com";
input string API_KEY = "";
input string FOLLOWER_ID = "";

// Copy Settings
input bool AUTO_COPY_ENABLED = true;
input double COPY_RATIO = 1.0;
input double RISK_PERCENT = 2.0;

// Risk Management
input double MAX_LOT_SIZE = 1.0;
input double MAX_DAILY_LOSS = 500.0;
input double MAX_DRAWDOWN = 20.0;
input int MAX_OPEN_POSITIONS = 10;

// System Settings
input int POLL_INTERVAL = 1;
input bool ENABLE_LOGGING = true;
```

## Common Components

### HTTP Client

```mql5
class CHttpClient {
private:
    string m_baseUrl;
    string m_apiKey;
    int m_timeout;
    
public:
    bool Get(string endpoint, string &response, int &statusCode);
    bool Post(string endpoint, string data, string &response, int &statusCode);
    bool SendRequestWithRetry(string method, string url, string headers, 
                             string data, string &response, int &statusCode, int maxRetries);
};
```

### Signal Data Structure

```mql5
struct SignalData {
    string signal_id;
    string provider_id;
    string action;          // "open", "close", "modify", "pending"
    string symbol;
    string type;            // "buy", "sell"
    double volume;
    double open_price;
    double stop_loss;
    double take_profit;
    string comment;
    int original_ticket;
    // Partial close fields
    double partial_close_volume;
    double remaining_volume;
    bool is_partial_close;
    string parent_signal_id;
    datetime timestamp;
};
```

### Logger

```mql5
class CLogger {
private:
    bool m_enabled;
    int m_logLevel;
    bool m_logToFile;
    string m_logFile;
    
public:
    void Debug(string message);
    void Info(string message);
    void Warning(string message);
    void Error(string message);
    void Critical(string message);
};
```

## Testing and Debugging

### EA Testing Script

```mql5
// Location: ea/tests/test_ea_functionality.mq5

void OnStart() {
    // Initialize test components
    InitializeTestComponents();
    
    // Run test suites
    TestLogger();
    TestHttpClient();
    TestSignalManager();
    TestApiEndpoints();
    
    // Print results
    PrintTestResults();
}
```

### Testing Checklist

#### Provider EA Tests
- [ ] Position detection and signal generation
- [ ] Partial close detection and handling
- [ ] Pending order tracking
- [ ] HTTP communication with backend
- [ ] Error handling and recovery
- [ ] Performance under load

#### Follower EA Tests
- [ ] Signal reception via HTTP polling
- [ ] Trade execution accuracy
- [ ] Risk management validation
- [ ] Partial close execution
- [ ] Error handling and recovery
- [ ] Performance monitoring

### Debugging Tools

#### 1. Logging
```mql5
// Enable debug logging
logger.SetLogLevel(LOG_LEVEL_DEBUG);
logger.SetLogToFile(true, "ea_debug.log");
```

#### 2. Print Statements
```mql5
Print("Signal received: ", signal.signal_id);
PrintFormat("Execution result: %s, Ticket: %d", 
           result.success ? "Success" : "Failed", result.ticket);
```

#### 3. Strategy Tester
- Use MT5 Strategy Tester for backtesting
- Test with historical data
- Validate signal generation accuracy

## Deployment Guide

### 1. Compilation

1. Open MetaEditor
2. Load EA source file
3. Press F7 or click Compile
4. Check for compilation errors
5. Verify .ex5 file is generated

### 2. Installation

1. Copy .ex5 file to `MT5_Data_Folder/MQL5/Experts/`
2. Copy configuration file to `MT5_Data_Folder/MQL5/Files/`
3. Restart MT5 terminal
4. EA should appear in Navigator

### 3. Configuration

1. Drag EA to chart
2. Configure input parameters
3. Enable auto-trading
4. Monitor EA logs

### 4. Monitoring

#### Key Metrics to Monitor
- Signal generation/reception rate
- Trade execution success rate
- Network connectivity status
- Error rates and types
- Performance metrics

#### Log Analysis
```bash
# Monitor EA logs
tail -f MT5_Data_Folder/MQL5/Files/ea_log.txt

# Check for errors
grep "ERROR" MT5_Data_Folder/MQL5/Files/ea_log.txt
```

## Best Practices

### Code Quality
- Use consistent naming conventions
- Add comprehensive comments
- Implement error handling
- Follow MQL5 coding standards

### Performance
- Minimize OnTick() processing time
- Use efficient data structures
- Implement proper memory management
- Cache frequently used data

### Security
- Validate all input parameters
- Use secure HTTP connections
- Implement proper authentication
- Sanitize user inputs

### Maintenance
- Regular code reviews
- Version control with Git
- Automated testing
- Performance monitoring

## Troubleshooting

### Common Issues

#### 1. Connection Problems
```
Error: "Failed to connect to API"
Solution: Check API_BASE_URL and network connectivity
```

#### 2. Authentication Failures
```
Error: "Invalid API key"
Solution: Verify API_KEY configuration and permissions
```

#### 3. Signal Processing Errors
```
Error: "Signal validation failed"
Solution: Check signal data format and validation rules
```

#### 4. Trade Execution Failures
```
Error: "Insufficient margin"
Solution: Check account balance and risk management settings
```

### Debug Steps

1. **Check Configuration**
   - Verify all input parameters
   - Validate API credentials
   - Check network connectivity

2. **Review Logs**
   - Enable debug logging
   - Check for error messages
   - Analyze execution flow

3. **Test Components**
   - Run EA testing script
   - Test individual functions
   - Validate API communication

4. **Monitor Performance**
   - Check system resources
   - Monitor response times
   - Validate trade execution

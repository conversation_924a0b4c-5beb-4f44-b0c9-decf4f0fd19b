# API Reference Documentation

## Overview

The Copy Trading System provides a comprehensive RESTful API for managing trading signals, users, and system operations. All API endpoints use JSON for request and response data.

## Base URL

```
Development: http://localhost:8000
Production: https://api.copytrading.com
```

## Authentication

### JWT Token Authentication

Most endpoints require JWT token authentication. Include the token in the Authorization header:

```http
Authorization: Bearer <your-jwt-token>
```

### API Key Authentication

MT5 Expert Advisors use API key authentication:

```http
Authorization: Bearer <your-api-key>
```

## Rate Limiting

- **General API**: 60 requests per minute
- **Authentication**: 10 requests per minute
- **Signal endpoints**: 100 requests per minute

Rate limit headers are included in responses:
```http
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 59
X-RateLimit-Reset: **********
```

## Error Handling

### Error Response Format

```json
{
  "detail": "Error message",
  "error_code": "VALIDATION_ERROR",
  "timestamp": "2023-12-01T12:00:00Z",
  "path": "/api/v1/signals/"
}
```

### HTTP Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict (duplicate)
- `422` - Validation Error
- `429` - Rate Limited
- `500` - Internal Server Error

## Authentication Endpoints

### POST /api/v1/auth/login

User login with email and password.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 1800,
  "user": {
    "id": "user-uuid",
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "user_type": "provider",
    "is_verified": true
  }
}
```

### POST /api/v1/auth/register

Register a new user account.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "first_name": "Jane",
  "last_name": "Smith",
  "user_type": "follower"
}
```

### GET /api/v1/auth/me

Get current user information.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "id": "user-uuid",
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe",
  "user_type": "provider",
  "is_verified": true,
  "created_at": "2023-01-01T00:00:00Z"
}
```

## Signal Endpoints

### POST /api/v1/signals/

Create a new trading signal (Provider EA endpoint).

**Headers:** `Authorization: Bearer <api-key>`

**Request:**
```json
{
  "provider_id": "provider_123",
  "action": "open",
  "symbol": "EURUSD",
  "type": "buy",
  "volume": 0.1,
  "open_price": 1.1234,
  "stop_loss": 1.1200,
  "take_profit": 1.1300,
  "comment": "Strong bullish signal",
  "original_ticket": 12345
}
```

**Partial Close Signal:**
```json
{
  "provider_id": "provider_123",
  "action": "close",
  "symbol": "EURUSD",
  "original_ticket": 12345,
  "partial_close_volume": 0.05,
  "remaining_volume": 0.05,
  "is_partial_close": true,
  "comment": "Partial profit taking"
}
```

**Pending Order Signal:**
```json
{
  "provider_id": "provider_123",
  "action": "pending",
  "symbol": "GBPUSD",
  "type": "buy",
  "volume": 0.2,
  "open_price": 1.2500,
  "stop_loss": 1.2450,
  "take_profit": 1.2600,
  "original_ticket": 67890
}
```

**Response:**
```json
{
  "success": true,
  "signal_id": "signal_abc123",
  "status": "distributed",
  "message": "Signal created and distributed successfully"
}
```

### GET /api/v1/signals/

List signals with filtering options.

**Query Parameters:**
- `provider_id` - Filter by provider
- `symbol` - Filter by trading symbol
- `action` - Filter by action (open, close, modify, pending)
- `status` - Filter by status
- `limit` - Number of results (default: 50, max: 100)
- `offset` - Pagination offset

**Response:**
```json
{
  "data": [
    {
      "signal_id": "signal_abc123",
      "provider_id": "provider_123",
      "action": "open",
      "symbol": "EURUSD",
      "type": "buy",
      "volume": 0.1,
      "status": "completed",
      "created_at": "2023-12-01T12:00:00Z"
    }
  ],
  "total": 150,
  "page": 1,
  "limit": 50,
  "total_pages": 3
}
```

### GET /api/v1/signals/{signal_id}

Get specific signal details.

**Response:**
```json
{
  "signal_id": "signal_abc123",
  "provider_id": "provider_123",
  "action": "open",
  "symbol": "EURUSD",
  "type": "buy",
  "volume": 0.1,
  "open_price": 1.1234,
  "stop_loss": 1.1200,
  "take_profit": 1.1300,
  "status": "completed",
  "created_at": "2023-12-01T12:00:00Z",
  "processed_at": "2023-12-01T12:00:01Z",
  "execution_results": [
    {
      "follower_id": "follower_456",
      "success": true,
      "executed_volume": 0.05,
      "executed_price": 1.1235
    }
  ]
}
```

## MT5 Specific Endpoints

### GET /api/v1/mt5/poll

Poll for new signals (Follower EA endpoint).

**Headers:** `Authorization: Bearer <api-key>`

**Query Parameters:**
- `follower_id` - Follower identifier
- `t` - Timestamp (cache busting)

**Response:**
```json
[
  {
    "signal_id": "signal_abc123",
    "action": "open",
    "symbol": "EURUSD",
    "type": "buy",
    "volume": 0.05,
    "open_price": 1.1234,
    "stop_loss": 1.1200,
    "take_profit": 1.1300,
    "copy_ratio": 0.5,
    "timestamp": "2023-12-01T12:00:00Z"
  }
]
```

### POST /api/v1/mt5/execution-result

Report signal execution result (Follower EA endpoint).

**Request:**
```json
{
  "execution_id": "exec_123",
  "signal_id": "signal_abc123",
  "follower_id": "follower_456",
  "success": true,
  "ticket": 98765,
  "executed_volume": 0.05,
  "executed_price": 1.1235,
  "error_message": null,
  "error_code": null
}
```

## Provider Endpoints

### GET /api/v1/providers/

List all providers.

**Query Parameters:**
- `is_verified` - Filter by verification status
- `is_active` - Filter by active status
- `limit`, `offset` - Pagination

### GET /api/v1/providers/{provider_id}

Get provider details.

### POST /api/v1/providers/register

Register provider with MT5 account info (Provider EA endpoint).

**Request:**
```json
{
  "provider_id": "provider_123",
  "account_number": ********,
  "broker": "MetaQuotes Demo",
  "server": "MetaQuotes-Demo",
  "balance": 10000.00,
  "equity": 10000.00
}
```

### POST /api/v1/providers/heartbeat

Provider heartbeat (Provider EA endpoint).

**Request:**
```json
{
  "provider_id": "provider_123",
  "status": "online",
  "balance": 10500.00,
  "equity": 10450.00,
  "timestamp": **********
}
```

### GET /api/v1/providers/{provider_id}/performance

Get provider performance metrics.

**Query Parameters:**
- `days` - Number of days (default: 30)

**Response:**
```json
[
  {
    "date": "2023-12-01",
    "total_profit": 150.50,
    "total_loss": -75.25,
    "total_trades": 25,
    "winning_trades": 18,
    "losing_trades": 7,
    "win_rate": 72.0,
    "profit_factor": 2.0,
    "max_drawdown": 5.5
  }
]
```

## Follower Endpoints

### GET /api/v1/followers/

List all followers.

### GET /api/v1/followers/{follower_id}

Get follower details.

### POST /api/v1/followers/register

Register follower with MT5 account info.

### PUT /api/v1/followers/{follower_id}/subscriptions

Update follower subscriptions.

**Request:**
```json
{
  "subscriptions": [
    {
      "provider_id": "provider_123",
      "copy_ratio": 0.5,
      "max_lot_size": 1.0,
      "is_active": true
    }
  ]
}
```

## Analytics Endpoints

### GET /api/v1/analytics/dashboard

Get dashboard analytics data.

**Response:**
```json
{
  "total_providers": 150,
  "active_providers": 120,
  "total_followers": 1500,
  "active_followers": 1200,
  "signals_today": 450,
  "trades_today": 1350,
  "system_health": {
    "api": "healthy",
    "database": "healthy",
    "redis": "healthy"
  }
}
```

### GET /api/v1/analytics/performance

Get system performance metrics.

## WebSocket Endpoints

### /ws/signals

Real-time signal updates for web dashboard.

**Connection:** `ws://localhost:8000/ws/signals?token=<jwt-token>`

**Message Format:**
```json
{
  "type": "signal_created",
  "data": {
    "signal_id": "signal_abc123",
    "provider_id": "provider_123",
    "action": "open",
    "symbol": "EURUSD"
  },
  "timestamp": "2023-12-01T12:00:00Z"
}
```

## Health Check Endpoints

### GET /health

Basic health check.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2023-12-01T12:00:00Z",
  "version": "2.0.0"
}
```

### GET /health/detailed

Detailed health check with component status.

**Response:**
```json
{
  "status": "healthy",
  "components": {
    "database": "healthy",
    "redis": "healthy",
    "external_apis": "healthy"
  },
  "timestamp": "2023-12-01T12:00:00Z"
}
```
